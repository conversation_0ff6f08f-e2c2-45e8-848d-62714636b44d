package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务查询结果
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobResponseDto {

    /**
     * 任务项列表
     */
    private List<JobItemResponseDto> itemList;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 任务类型 com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum
     */
    private String type;
}
