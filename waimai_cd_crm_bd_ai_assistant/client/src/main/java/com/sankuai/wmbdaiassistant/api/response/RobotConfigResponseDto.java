package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机器人配置
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-11-22 11:18
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class RobotConfigResponseDto extends BeeResponseData {

    /**
     * 摩西机器人ID
     */
    @ThriftField(1)
    private String id;

    /**
     * 机器人的URL
     */
    @ThriftField(2)
    private String aiUrl;

    /**
     * 机器人类型，0：摩西机器人 1：智能客服 2：小蜜AI
     */
    @ThriftField(3)
    private Integer robotType;

    /**
     * 用户是否在灰度城市内
     */
    @ThriftField(4)
    private Boolean isInGray;
}
