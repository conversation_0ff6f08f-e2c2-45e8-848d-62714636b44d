package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商家选择器
 *
 * @author: wb_zhousong05
 * @date 2025-06-18 10:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class PoiSelectorResponseDto extends BeeResponseData {
    private PoiSelectorDto poiSelector;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ThriftStruct
    public static class PoiSelectorDto {
        /**
         * @see com.sankuai.wmbdaiassistant.api.enums.PoiSelectorTypeEnum MultiSelect: 多选；SingleSelect: 单选
         */
        private String type;
        private List<PoiInfoDto> defaultList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ThriftStruct
    public static class PoiInfoDto {
        private Long id;
        private String name;
        private String url;
        private List<String> tagNameList;
    }

}
