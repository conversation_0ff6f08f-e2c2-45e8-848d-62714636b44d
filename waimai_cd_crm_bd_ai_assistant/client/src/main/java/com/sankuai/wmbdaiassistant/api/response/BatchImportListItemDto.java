package com.sankuai.wmbdaiassistant.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准问批量导入的数据项
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-27 10:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportListItemDto {

    /**
     * 数据项ID
     */
    private Long id;

    /**
     * 域
     */
    private Long domainId;

    /**
     * 问题
     */
    private String question;

    /**
     * 回复
     */
    private String answer;

    /**
     * TT链接
     */
    private String ttUrl;

    /**
     * 相似问题id
     */
    private Long phraseId;

    /**
     * 相似问题
     */
    private String similarQuestion;

    /**
     * 相似答案
     */
    private String similarAnswer;

    /**
     * 是否替换相似问题，0：否，1：是
     */
    private Integer replace;

    /**
     * 类型
     */
    private String type;
}
