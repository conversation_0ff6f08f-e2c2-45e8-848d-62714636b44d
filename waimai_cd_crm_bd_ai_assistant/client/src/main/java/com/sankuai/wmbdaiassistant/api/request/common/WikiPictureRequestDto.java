package com.sankuai.wmbdaiassistant.api.request.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wmbdaiassistant.api.request.BeeRequestDto;
import lombok.Data;

/**
 * 学城图片链接转化请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 7/8/25 3:32 AM
 */
@Data
@ThriftStruct
public class WikiPictureRequestDto extends BeeRequestDto {

    /**
     *
     */
    @ThriftField(2)
    private String wikiPictureUrl;

}
