package com.sankuai.wmbdaiassistant.api.response.hotquestion;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 高频问配置信息
 * <AUTHOR>
 * @Date 2025/6/17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class HotQuestionConfigItemDto {

    /** 类型 **/
    private Integer questionType;

    /** 标准问id **/
    private Long phraseId;

    /** 展示内容 **/
    private String content;

    /** 跳转链接 **/
    private String jumpUrl;

    /**高亮展示开始日期，格式为“yyyy-MM-dd HH:mm:ss” **/
    private String highlightStartTime;

    /**高亮展示截止日期，格式为“yyyy-MM-dd HH:mm:ss” **/
    private String highlightEndTime;

    /** icon图案 **/
    private String iconUrl;

}
