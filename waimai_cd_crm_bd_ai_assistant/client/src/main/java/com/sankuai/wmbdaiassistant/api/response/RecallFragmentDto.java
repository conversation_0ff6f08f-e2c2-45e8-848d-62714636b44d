package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import java.util.List;
import lombok.Data;

/**
 * 召回的分片（markdown格式）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-14 10:58
 */
@Data
@ThriftStruct
public class RecallFragmentDto {

    /**
     * 分片内容
     */
    @ThriftField(1)
    private String content;

    /**
     * 分片得分
     */
    @ThriftField(2)
    private Double score;

    /**
     * ES 主键 规则为: {wikiId}-{batchId}-{chunkId}
     */
    @ThriftField(3)
    private String id;

    /**
     * 标题
     */
    @ThriftField(4)
    private String title;

    /**
     * wiki ID
     */
    @ThriftField(5)
    private Long wikiId;

    /**
     * 分片ID
     */
    @ThriftField(6)
    private Integer chunkId;

    /**
     * 标签
     */
    @ThriftField(7)
    private List<String> tags;

    /**
     * 数据集ID
     */
    @ThriftField(8)
    private Long datasetId;
}
