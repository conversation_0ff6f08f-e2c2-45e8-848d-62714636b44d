package com.sankuai.wmbdaiassistant.api.request.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import java.util.List;
import lombok.Data;

/**
 * 摩西评论请求参数
 *
 * <AUTHOR> <wb_<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:23
 */
@Data
@ThriftStruct
public class BeeMosesCommentRequestDto {

    /**
     * 租户ID
     */
    @ThriftField(1)
    private Long tenantId;

    /**
     * 租户名字
     */
    @ThriftField(2)
    private String tenantName;

    /**
     * 业务ID
     */
    @ThriftField(3)
    private Long bizId;

    /**
     * 业务名称
     */
    @ThriftField(4)
    private String bizName;

    /**
     * 摩西机器人ID/智能客服url
     */
    @ThriftField(5)
    private String robotId;

    /**
     * 提交的类型，close：直接关闭，submit：提交。
     */
    @ThriftField(6)
    private String type;

    /**
     * 评级（1 ~ 5星）
     */
    @Deprecated
    @ThriftField(7)
    private Integer star;

    /**
     * 描述
     */
    @ThriftField(8)
    private String desc;

    /**
     * 评论
     */
    @ThriftField(9)
    private String comment;

    /**
     * 评论的快捷语
     */
    @ThriftField(10)
    private List<String> tips;

    /**
     * 机器人类型。0：摩西机器人 1：智能客服
     */
    @ThriftField(11)
    private Integer robotType;

    /**
     * 值
     */
    @ThriftField(12)
    private Integer value;

}
