package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * 任务相关thrift接口
 *
 * <AUTHOR>
 * @date 2025-07-08 10:11
 */
@ThriftService
public interface JobThriftService {

    @MethodDoc(
            displayName = "",
            description = "获取今日商家诊断任务列表",
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto listPoiDiagnosisJobsToday(Long sessionId);

    @MethodDoc(
            displayName = "",
            description = "获取商家诊断今日运行中任务个数",
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto countPoiDiagnosisTodayRunningJobs();

    @MethodDoc(
            displayName = "",
            description = "记录蜜蜂首页 小蜜入口 诊断任务全部完成 弹窗事件",
            returnValueDescription = "",
            example = "无"
    )
    @ThriftMethod
    BeeResponseDto recordPopup();
}
