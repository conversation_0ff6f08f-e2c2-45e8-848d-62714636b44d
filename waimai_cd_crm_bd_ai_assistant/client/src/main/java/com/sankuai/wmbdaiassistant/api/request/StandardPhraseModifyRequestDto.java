package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 标准问修改请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 15:34
 */
@Data
public class StandardPhraseModifyRequestDto {

    private Long id;
    private Long domainId;
    private String question;
    private String answer;
    private String ttUrl;
    /**
     * 类型
     */
    private String type;
    private Long phraseId;

    public FaqModifyRequestDto toFaqRequestDto() {
        FaqModifyRequestDto requestDto = new FaqModifyRequestDto();

        requestDto.setId(id);
        requestDto.setDomainId(domainId);
        requestDto.setQuestion(question);
        requestDto.setAnswer(answer);
        requestDto.setTtUrl(ttUrl);
        requestDto.setPhraseId(phraseId);

        return requestDto;
    }

    public TaskModifyRequestDto toTaskRequestDto() {
        TaskModifyRequestDto requestDto = new TaskModifyRequestDto();
        requestDto.setDomainId(domainId);
        requestDto.setId(id);
        requestDto.setQuestion(question);

        return requestDto;
    }

}
