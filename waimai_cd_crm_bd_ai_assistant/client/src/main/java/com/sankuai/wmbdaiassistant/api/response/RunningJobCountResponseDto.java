package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运行中任务项数量查询结果
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RunningJobCountResponseDto extends BeeResponseData {

    /**
     * 正在运行中的任务个数
     */
    private Long runnings;

    /**
     * 是否需要 click ，当为 true 时，表明存在已经完成，但是还没有查看的任务列表
     */
    private Boolean needToClick;
}
