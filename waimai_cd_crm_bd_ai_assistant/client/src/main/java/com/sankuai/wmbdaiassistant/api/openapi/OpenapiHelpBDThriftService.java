package com.sankuai.wmbdaiassistant.api.openapi;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ExtResponse;

/**
 * 【开放平台】扩展能力
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><EMAIL>>
 * @date 2025/6/19 14:40
 */
@ThriftService
public interface OpenapiHelpBDThriftService {

    /**
     * 查询工单信息
     * @param ticketId 工单ID
     * @return 工单信息JsonNode
     */
    @ThriftMethod
    ExtResponse<JsonNode> queryTicket(Integer ticketId);
}
