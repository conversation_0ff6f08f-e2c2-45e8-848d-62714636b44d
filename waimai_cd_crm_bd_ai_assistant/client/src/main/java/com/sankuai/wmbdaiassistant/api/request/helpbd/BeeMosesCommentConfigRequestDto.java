package com.sankuai.wmbdaiassistant.api.request.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 获取评论配置的请求参数
 *
 * <AUTHOR> <wb_<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:24
 */
@Data
@ThriftStruct
public class BeeMosesCommentConfigRequestDto {

    /**
     * 租户ID
     */
    @ThriftField(1)
    private Long tenantId;

    /**
     * 租户名字
     */
    @ThriftField(2)
    private String tenantName;

    /**
     * 业务ID
     */
    @ThriftField(3)
    private Long bizId;

    /**
     * 业务名称
     */
    @ThriftField(4)
    private String bizName;

    /**
     * 是否使用新配置
     */
    @ThriftField(5)
    private Boolean newConfig;
}
