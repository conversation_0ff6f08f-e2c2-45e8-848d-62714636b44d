package com.sankuai.wmbdaiassistant.api.response.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 摩西机器人相关信息
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:38
 */
@Data
@ThriftStruct
public class BeeMosesRobotDto {

    /**
     * 摩西机器人ID
     */
    @ThriftField(1)
    private String id;

    /**
     * 智能客服的URL
     */
    @ThriftField(2)
    private String aiUrl;

    /**
     * 机器人类型，0：摩西机器人 1：智能客服
     */
    @ThriftField(3)
    private Integer robotType;

    /**
     * 用户是否在灰度城市内
     */
    @ThriftField(4)
    private Boolean isInGray;

    public BeeMosesRobotDto() {
    }

    public BeeMosesRobotDto(String id) {
        this.id = id;
    }
}
