package com.sankuai.wmbdaiassistant.api.response.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.ToString;

/**
 * Thrift 接口的返回体-在线提问功能迁移
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-02-28 10:37
 */
@Data
@ToString
@ThriftStruct
public class ThriftCommonDto<T> extends ThriftBaseDto {

    @FieldDoc(
            description = "详细数据"
    )
    @ThriftField(3)
    private T data;

}
