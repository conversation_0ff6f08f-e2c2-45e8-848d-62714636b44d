package com.sankuai.wmbdaiassistant.api.response.helpbd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * web统一响应结果-在线提问功能迁移
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:41
 */
@JsonInclude(Include.NON_NULL)
public class ExtResponse<T> {

    public static final int SUCCESS = 0;
    public static final int FAILED = 1;
    private static final String SUCCESS_MSG = "成功";

    private Integer code ;
    private String msg;
    private T data;
    private String originCode;
    private String detailMsg;

    // 添加公共无参构造函数，用于Thrift序列化
    public ExtResponse() {
    }

    private ExtResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> ExtResponse<T> success() {
        return new ExtResponse<>(SUCCESS, SUCCESS_MSG, null);
    }

    public static <T> ExtResponse<T> success(T data) {
        return new ExtResponse<>(SUCCESS, SUCCESS_MSG, data);
    }

    public static <T> ExtResponse<T> fail(String msg) {
        return new ExtResponse<>(FAILED, msg, null);
    }

    public static <T> ExtResponse<T> of(Integer code, String msg) {
        return new ExtResponse<>(code, msg, null);
    }

    public static <T> ExtResponse<T> of(Integer code, String msg, T data) {
        return new ExtResponse<>(code, msg, data);
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public String getOriginCode() {
        return originCode;
    }

    public ExtResponse<T> setOriginCode(String originCode) {
        this.originCode = originCode;
        return this;
    }

    public String getDetailMsg() {
        return detailMsg;
    }

    public ExtResponse<T> setDetailMsg(String detailMsg) {
        this.detailMsg = detailMsg;
        return this;
    }
}
