package com.sankuai.wmbdaiassistant.api.response.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.ToString;

/**
 * Thrift 接口的最基本的响应体-在线提问功能迁移
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-02-28 17:37
 */
@Data
@ToString
@ThriftStruct
public class ThriftBaseDto {

    @FieldDoc(
            description = "状态码，0-成功、1-失败",
            example = {"0"}
    )
    @ThriftField(1)
    private Integer code;

    @FieldDoc(
            description = "错误消息",
            example = {"成功"}
    )
    @ThriftField(2)
    private String msg;
}
