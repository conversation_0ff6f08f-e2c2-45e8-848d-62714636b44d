package com.sankuai.wmbdaiassistant.api.request.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 获取摩西机器人的请求参数
 *
 * <AUTHOR> <wb_<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:21
 */
@Data
@ThriftStruct
public class BeeMosesRequestDto {

    /**
     * 租户ID
     */
    @ThriftField(1)
    private Long tenantId;

    /**
     * 租户名字
     */
    @ThriftField(2)
    private String tenantName;

    /**
     * 业务ID
     */
    @ThriftField(3)
    private Long bizId;

    /**
     * 业务名称
     */
    @ThriftField(4)
    private String bizName;

    /**
     * 系统名称，枚举值：android，ios
     */
    @ThriftField(5)
    private String sysName;

    /**
     * 系统版本号
     */
    @ThriftField(6)
    private String sysVer;

    /**
     * 请求来源的app名称
     */
    @ThriftField(7)
    private String appName;

    /**
     * 请求来源app版本号
     */
    @ThriftField(8)
    private String appVer;

    /**
     * 请求来源入口
     */
    @ThriftField(9)
    private String source;

}
