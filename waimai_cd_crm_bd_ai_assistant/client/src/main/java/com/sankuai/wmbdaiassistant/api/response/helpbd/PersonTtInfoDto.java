package com.sankuai.wmbdaiassistant.api.response.helpbd;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.Map;
import lombok.Data;

/**
 * 用户 TT 工单信息
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:52
 */
@Data
public class PersonTtInfoDto {

    /**
     * 用户标识
     */
    private Integer uid;

    /**
     * 用户mis
     */
    private String misId;

    /**
     * 未解决的 TT 工单数
     */
    private Integer unresolved;

    /**
     * 我发起的 TT 工单数
     */
    private Integer reporter;

    /**
     * TT schema 额外参数
     */
    private Map<String, Integer> ttSchemeParams;
}
