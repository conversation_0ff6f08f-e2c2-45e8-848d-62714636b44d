package com.sankuai.wmbdaiassistant.api.response;


import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务项查询结果
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobItemResponseDto {

    /**
     * 商家ID
     */
    private Integer poiId;

    /**
     * 商家名称
     */
    private String poiName;

    /**
     * 商家头像
     */
    private String poiAvator;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 能力类型
     */
    private String abilityType;

    /**
     * 用户点击选项之后的操作类型：1.url跳转，2.继续提问
     */
    private String operationType;

    private String content;

}
