package com.sankuai.wmbdaiassistant.api.response;


import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务列表查询结果
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobListResponseDto extends BeeResponseData {
    /**
     * 任务列表
     */
    private List<JobResponseDto> jobList;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 成功个数
     */
    private Integer success;

    /**
     * 失败个数
     */
    private Integer fail;


}
