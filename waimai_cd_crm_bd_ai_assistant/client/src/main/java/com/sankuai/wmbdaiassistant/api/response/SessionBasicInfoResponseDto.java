package com.sankuai.wmbdaiassistant.api.response;

import java.util.List;

import lombok.Data;

/**
 * 会话基本信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-24 15:18
 */
@Data
public class SessionBasicInfoResponseDto {

    /**
     * 会话ID
     */
    private Long id;

    /**
     * mis
     */
    private String mis;

    /**
     * 名称
     */
    private String name;

    /**
     * 组织架构信息
     */
    private List<String> orgInfos;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 是否提交过TT
     */
    private Boolean submittedTt;

    /**
     * 是否备注过
     */
    private Boolean marked;

    /**
     * 备注内容
     */
    private String remark;

    /**
     * 标准问列表
     */
    private List<String> phraseNameList;

    /**
     * 域列表
     */
    private List<String> domainList;

    /**
     * TT工单链接列表
     */
    private List<TtLinkDto> ttLinkList;

}
