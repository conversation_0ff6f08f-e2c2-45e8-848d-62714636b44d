package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeEntryTrackingDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeMosesCommentConfigRequestDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeMosesCommentRequestDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeUserDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.BeeMosesCommentStarConfigDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ExtResponse;
import com.sankuai.wmbdaiassistant.api.response.helpbd.PersonTtInfoDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftBaseDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftCommonDto;
import java.util.List;

/**
 * 扩展接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 14:02
 */
@ThriftService
public interface HelpBDControlThriftService {

    /**
     * 获取TT信息
     *
     * @return TT信息
     */
    @ThriftMethod
    ExtResponse<PersonTtInfoDto> getTtInfo();
    /**
     * 摩西服务评论
     *
     * @param beeUser 用户信息
     * @param mosesCommentRequestDto 评论信息
     * @return
     */
    @ThriftMethod
    ThriftBaseDto mosesComment(BeeUserDto beeUser, BeeMosesCommentRequestDto mosesCommentRequestDto);

    /**
     * 获取当前的评论配置
     *
     * 1. 获取评论配置
     * 2. 会同时执行限频操作，如果用户被限频了，也将无法获取到配置
     *
     * @param beeUser
     */
    @ThriftMethod
    ThriftCommonDto<List<BeeMosesCommentStarConfigDto>> fetchMosesCommentConfig(BeeUserDto beeUser, BeeMosesCommentConfigRequestDto requestDto);

    /**
     * 机器人埋点入口
     * @param beeUser 用户信息
     * @param beeEntryTrackingDto 入口埋点信息
     */
    @ThriftMethod
    void entryTracking(BeeUserDto beeUser, BeeEntryTrackingDto beeEntryTrackingDto);
}
