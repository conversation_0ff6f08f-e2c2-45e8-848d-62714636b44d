package com.sankuai.wmbdaiassistant.api.request.helpbd;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 入口点埋点
 *
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 14:37
 */
@Data
@ThriftStruct
public class BeeEntryTrackingDto {

    /**
     * 租户ID
     */
    @ThriftField(1)
    private Long tenantId;

    /**
     * 租户名字
     */
    @ThriftField(2)
    private String tenantName;

    /**
     * 业务ID
     */
    @ThriftField(3)
    private Long bizId;

    /**
     * 业务名称
     */
    @ThriftField(4)
    private String bizName;

    /**
     * 来源
     */
    @ThriftField(5)
    private String source;

    /**
     * 摩西机器人ID/智能客服url
     */
    @ThriftField(6)
    private String robotId;

    /**
     * 机器人类型。0：摩西机器人 1：智能客服
     */
    @ThriftField(7)
    private Integer robotType;
}
