package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 隐藏文本片段
 * 格式参照：https://km.sankuai.com/collabpage/2716980230
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-15 16:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HideSpan implements ContentElement {
    private final String type = ContentElementTypeEnum.HIDE_SPAN.getCode();

    @JsonProperty("insert")
    private String text;

    public static HideSpan build(String text) {
        return HideSpan.builder().text(text).build();
    }

    @Override
    public String toString() {
        return text;
    }

    @Override
    public String toMarkdownText() {
        return String.format("(%s)", DefaultUtil.defaultValue(text, ""));
    }

    @Override
    public String getType() {
        return type;
    }
}

