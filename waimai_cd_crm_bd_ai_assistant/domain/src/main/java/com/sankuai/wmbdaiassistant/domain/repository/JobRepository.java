package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.JobModel;

import java.util.Date;
import java.util.List;

/**
 * job仓储
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
public interface JobRepository {

    /**
     * 根据mis和type和日期范围查询
     *
     * @param mis
     * @param type
     * @param startDate
     * @param endDate
     * @return
     */
    List<JobModel> findByMisAndTypeAndDateRange(String mis, String type, Date startDate, Date endDate);


    /**
     * 插入
     *
     * @param jobModel
     * @return
     */
    Long insert(JobModel jobModel);
}
