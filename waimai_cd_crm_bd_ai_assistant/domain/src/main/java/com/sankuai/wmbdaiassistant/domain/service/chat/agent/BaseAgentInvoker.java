package com.sankuai.wmbdaiassistant.domain.service.chat.agent;

import com.sankuai.wmbdaiassistant.domain.bo.AgentInvokeBo;
import com.sankuai.wmbdaiassistant.domain.model.BizAgentModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;

public interface BaseAgentInvoker {

    /**
     * 调用agent
     */
    void invoke(AgentInvokeBo agentInvokeBo, BizAgentModel bizAgentModel, GeneralCallback callback);

}
