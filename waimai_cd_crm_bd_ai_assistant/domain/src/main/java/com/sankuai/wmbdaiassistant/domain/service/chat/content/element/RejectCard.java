package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 审核驳回建议卡片
 * 格式参照：https://km.sankuai.com/collabpage/2716980230#b-a6b2d4e2dc1f4abebcf820aae908759e
 *
 * <AUTHOR>
 * @date 2025-07-10 16:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RejectCard implements ContentElement {

    private final String type = ContentElementTypeEnum.RejectCard.getCode();

    @JsonProperty("insert")
    private RejectCardInsert insert;

    @Override
    public String toMarkdownText() {
        if (insert == null) {
            return null;
        }
        return JsonUtil.toJson(insert);
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RejectCardInsert  {
        private RejectCardObject rejectCard;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RejectCardObject  {
        private List<RejectCardContent> content;
        private String extendButtonName;
        private Integer showNum;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RejectCardContent {
        private String title;
        private String content; // md字符串
        private List<Descriptions.LabelValueObject> descriptions;
        private ButtonGroup.ButtonInfo button;
    }

}
