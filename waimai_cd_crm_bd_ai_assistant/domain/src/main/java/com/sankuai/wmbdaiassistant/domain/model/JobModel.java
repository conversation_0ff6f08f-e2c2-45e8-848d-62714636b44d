package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * Job
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobModel {
    /**
     *   主键id
     */
    private Long id;

    /**
     *   任务类型
     */
    private JobTypeEnum type;

    /**
     *   uid
     */
    private Integer uid;

    /**
     *   mis
     */
    private String mis;

    /**
     *   创建时间
     */
    private Date ctime;

    /**
     *   更新时间
     */
    private Date utime;
}
