package com.sankuai.wmbdaiassistant.domain.service.hotquestion;

import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigSaveRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;

import java.util.List;
import java.util.Set;

/**
 * 热门问服务
 *
 * <AUTHOR>
 * @date 2025-06-17 15:44
 */
public interface HotQuestionService {

    Set<String> getAllAssistantBiz();

    HotQuestionConfigBo getConfigByAssistantBiz(String assistantBiz);

    void saveConfigByAssistantBiz(HotQuestionConfigSaveRequestBo requestBo);

    List<HotQuestionBo> getHotQuestionList(Integer chooseBizId, SessionBo sessionBo);

    Set<Long> getFixedAnswerPhraseIdList(Integer chooseBizId, SessionBo sessionBo);

}
