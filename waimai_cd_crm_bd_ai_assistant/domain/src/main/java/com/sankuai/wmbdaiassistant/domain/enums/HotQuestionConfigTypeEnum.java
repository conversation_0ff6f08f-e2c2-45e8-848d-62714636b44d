package com.sankuai.wmbdaiassistant.domain.enums;

import java.util.Objects;

public enum HotQuestionConfigTypeEnum {
    WORKFLOW(1, "多轮", AbilityTypeEnum.GENERAL, OperationTypeEnum.CONTINUE_QUERY),
    FIXED_ANSWER(2, "固定答案", AbilityTypeEnum.GENERAL, OperationTypeEnum.CONTINUE_QUERY),
    RAG(3, "RAG", AbilityTypeEnum.GENERAL, OperationTypeEnum.CONTINUE_QUERY),
    JUMP_LINK(4,"跳转链接", AbilityTypeEnum.JUMP, OperationTypeEnum.JUMP),
    ;

    private int code;
    private String desc;
    private AbilityTypeEnum abilityTypeEnum;
    private OperationTypeEnum operationTypeEnum;

    HotQuestionConfigTypeEnum(int code, String desc, AbilityTypeEnum abilityTypeEnum, OperationTypeEnum operationTypeEnum) {
        this.code = code;
        this.desc = desc;
        this.abilityTypeEnum = abilityTypeEnum;
        this.operationTypeEnum = operationTypeEnum;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public AbilityTypeEnum getAbilityTypeEnum() {
        return abilityTypeEnum;
    }

    public OperationTypeEnum getOperationTypeEnum() {
        return operationTypeEnum;
    }

    public static HotQuestionConfigTypeEnum findByCode(int code) {
        for (HotQuestionConfigTypeEnum type : HotQuestionConfigTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static boolean needQueryPhrase(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return WORKFLOW.code == code || FIXED_ANSWER.code == code;
    }

}