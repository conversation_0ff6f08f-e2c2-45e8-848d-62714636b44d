package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 意图识别返回类型枚举
 */
@Getter
public enum IntentRecognitionRespTypeEnum {
    GUIDE("guide", "意图引导"),
    COLLECTION("collection", "参数搜集"),
    WORKFLOW("workflow", "技能调用"),
    AGENT("agent", "调用其他agent")
    ;

    private String code;
    private String desc;

    IntentRecognitionRespTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
