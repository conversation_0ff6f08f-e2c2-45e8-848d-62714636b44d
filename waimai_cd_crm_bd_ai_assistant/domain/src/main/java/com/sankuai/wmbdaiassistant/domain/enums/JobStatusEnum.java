package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 任务类型枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/07/08
 */
@Getter
public enum JobStatusEnum {
    INIT("init", "执行中"),
    SUCCESS("success", "成功"),
    FAIL("fail", "失败");

    private String code;
    private String desc;

    JobStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static JobStatusEnum getByCode(String code) {
        for (JobStatusEnum status : JobStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
