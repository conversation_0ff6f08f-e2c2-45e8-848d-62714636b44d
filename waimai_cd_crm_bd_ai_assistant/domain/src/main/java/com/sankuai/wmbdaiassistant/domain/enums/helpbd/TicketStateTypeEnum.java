package com.sankuai.wmbdaiassistant.domain.enums.helpbd;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TicketStateTypeEnum {
    /**
     * 未处理
     */
    INIT("未处理"),
    /**
     * 处理中
     */
    HANDING("处理中"),
    /**
     * 挂起中
     */
    HANGUP("挂起中"),
    /**
     * 暂停中
     */
    PAUSE("暂停中"),
    /**
     * 重新打开
     */
    REOPEN("重新打开"),
    /**
     * 已解决
     */
    SOLVED("已解决"),
    /**
     * 已关闭
     */
    CLOSED("已关闭"),

    ;

    private final String msg;
}
