package com.sankuai.wmbdaiassistant.domain.enums.helpbd;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 入口点的类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-02 10:55
 */
@Getter
public enum EntryTrackingTypeEnum {

    BEE_MY_HELP("bee_my_help", "蜜蜂在线提问入口"),
    BEE_POI_DETAIL("bee_poi_detail", "蜜蜂商家详情入口"),
    BEE_WORKBENCH("bee_workbench", "蜜蜂工作台入口"),
    DAXIANG_WORKBENCH("daxiang_workbench", "大象工作台入口"),
    ;

    private final String code;
    private final String desc;

    EntryTrackingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EntryTrackingTypeEnum getByCode(String code) {
        for (EntryTrackingTypeEnum entryTrackingTypeEnum : values()) {
            if (StringUtils.equalsIgnoreCase(entryTrackingTypeEnum.getCode(), code)) {
                return entryTrackingTypeEnum;
            }
        }
        return null;

    }
}
