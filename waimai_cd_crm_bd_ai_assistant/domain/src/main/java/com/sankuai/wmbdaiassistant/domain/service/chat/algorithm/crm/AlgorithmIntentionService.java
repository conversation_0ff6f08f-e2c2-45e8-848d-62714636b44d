package com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.model.IntentRecognitionRespData;

import java.util.function.Consumer;

public interface AlgorithmIntentionService {

    /**
     * 流式意图识别接口
     * 
     * @param sessionBo
     * @param input
     * @param outputConsumer
     * @param onError
     * @return
     */
    boolean recognizeIntentionStream(SessionBo sessionBo, String input,
            Consumer<IntentRecognitionRespData> outputConsumer, Consumer<Throwable> onError);
}
