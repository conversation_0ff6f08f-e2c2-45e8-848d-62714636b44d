package com.sankuai.wmbdaiassistant.domain.model;

import com.sankuai.wmbdaiassistant.domain.enums.JobStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * JobItem
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobItemModel {
    /**
     *   主键id
     */
    private Long id;

    /**
     *   任务id
     */
    private Long jobId;

    /**
     *   任务类型
     */
    private JobTypeEnum type;

    /**
     *   uid
     */
    private Integer uid;

    /**
     *   用户mis
     */
    private String mis;

    /**
     *   实体类型
     */
    private String entityType;

    /**
     *   实体id
     */
    private String entityId;

    /**
     *   任务状态
     */
    private JobStatusEnum status;


    private JobTriggerModel trigger;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;

    /**
     *   字段: utime
     *   说明: 更新时间
     */
    private Date utime;
}
