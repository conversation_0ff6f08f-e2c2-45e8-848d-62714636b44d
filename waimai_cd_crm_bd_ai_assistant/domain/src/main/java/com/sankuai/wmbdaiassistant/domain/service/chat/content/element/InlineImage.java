package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InlineImage implements ContentElement {
    private final String type = ContentElementTypeEnum.INLINE_IMAGE.getCode();

    @JsonProperty("insert")
    private InlineImageInfo info;

    public static InlineImage build(String url) {
        return InlineImage.builder().info(InlineImageInfo.builder().url(url).build()).build();
    }

    @Override
    public String toString() {
        return info == null ? "" : info.url;
    }

    @Override
    public String toMarkdownText() {
        return String.format("![图片](%s)", info == null ? "" : DefaultUtil.defaultValue(info.url, ""));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InlineImageInfo {
        @JsonProperty("inlineImage")
        private String url;
    }


    @Override
    public Map<String, String> toTtTransferContent() {
        Map<String, String> map = new HashMap<>();
        map.put("type", "inlineImage");
        map.put("url", info.getUrl());
        return map;
    }
}
