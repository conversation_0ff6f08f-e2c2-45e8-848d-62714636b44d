package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebView组件
 * 格式参照：https://km.sankuai.com/collabpage/2716980230
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-15 16:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebView implements ContentElement {
    private final String type = ContentElementTypeEnum.WEBVIEW.getCode();

    @JsonProperty("insert")
    private WebViewInfo insert;

    public static WebView build(String url) {
        return WebView.builder()
                .insert(WebViewInfo.builder()
                        .webview(WebViewData.builder()
                                .url(url)
                                .build())
                        .build())
                .build();
    }

    @Override
    public String toString() {
        return insert != null && insert.getWebview() != null ?
                DefaultUtil.defaultValue(insert.getWebview().getUrl(), "") : "";
    }

    @Override
    public String toMarkdownText() {
        if (insert == null || insert.getWebview() == null) {
            return "";
        }
        String url = DefaultUtil.defaultValue(insert.getWebview().getUrl(), "");
        return String.format("[%s](%s)", url, url);
    }

    @Override
    public String getType() {
        return type;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebViewInfo {
        private WebViewData webview;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebViewData {
        private String url;
    }
}

