package com.sankuai.wmbdaiassistant.domain.service.chat.content.element;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElement;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ContentElementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ActionCard
 *
 * @see <a href="https://km.sankuai.com/collabpage/2263992064#b-cb14b059f3c247978aad2a8d67c831a2">ActionCard</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCard implements ContentElement {

    private final String type = ContentElementTypeEnum.ACTION_CARD.getCode();

    @JsonProperty("insert")
    private ActionCardData insert;

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String toMarkdownText() {
        return "";
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionCardData {
        private ActionCardInfo actionCard;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionCardInfo {
        private ButtonGroup.ButtonInfo button;
        private String title;
        private String subTitle;
        /**
         * HexColor
         */
        private String backgroundColor;
    }
}
