package com.sankuai.wmbdaiassistant.domain.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * wiki分片服务，由于算法侧未提供响应的具体模型，因此自定义模型承载wiki分片请求响应结果
 */
@Setter
@Getter
public class AlgorithmFragmentModel {

    @JsonAlias("wiki_id")
    private Long wikiId;

    @JsonAlias("chunk_id")
    private Integer chunkId;

    private String content;

    @JsonAlias("chunk_mkd_title")
    private String chunkMkdTitle;

    private List<String> tags;
}
