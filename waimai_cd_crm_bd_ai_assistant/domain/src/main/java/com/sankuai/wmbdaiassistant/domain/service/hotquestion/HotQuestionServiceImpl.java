package com.sankuai.wmbdaiassistant.domain.service.hotquestion;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.AssistantBizMappingBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigItem;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigSaveRequestBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.enums.HotQuestionConfigTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.PlatformEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.model.crm.platform.CrmPlatformUser;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.repository.TraceLogRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.TraceLogQuery;
import com.sankuai.wmbdaiassistant.domain.service.chat.user.WmUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 热门问服务
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class HotQuestionServiceImpl implements HotQuestionService {

    @MdpConfig("assistantBiz.crmBiz.map:{\"5001\":\"直营白领\",\"5002\":\"直营校园\",\"5005\":\"全国KA\",\"5006\":\"区域KA\",\"5016\":\"拼好饭\"}")
    private HashMap<Integer, String> crmBizIdAssistantBizNameMap;

    @MdpConfig("assistant.hotQuestionConfig.map:{}")
    private HashMap<String, HotQuestionConfigBo> assistantHotQuestionMap;

    @MdpConfig("hot.question.default.icon.url:https://s3plus.meituan.net/bdaiassistant-public/hotQuestionIcon.png")
    private String hotQuestionDefaultIconUrl;

    @Value("$KMS{lion_account}")
    private String lionAccount;

    @Value("$KMS{lion_secret}")
    private String lionSecret;

    private static final String HOT_QUESTION_CONFIG_LION_KEY = "assistant.hotQuestionConfig.map";

    private static final Integer ZHIYING_BAI_LING_BIZ_ID = 5001;

    @Autowired
    private SessionSourceConfigManager sessionSourceConfigManager;

    @Autowired
    private WmUserService wmUserService;

    @Resource
    private PhraseRepository phraseRepository;

    @Resource
    private TraceLogRepository traceLogRepository;


    @Override
    public Set<String> getAllAssistantBiz() {
        return new HashSet<>(crmBizIdAssistantBizNameMap.values());
    }

    @Override
    public HotQuestionConfigBo getConfigByAssistantBiz(String assistantBiz) {
        HotQuestionConfigBo hotQuestionConfigBo = assistantHotQuestionMap.get(assistantBiz);
        if (hotQuestionConfigBo == null || CollectionUtils.isEmpty(hotQuestionConfigBo.getItemList())) {
            return new HotQuestionConfigBo();
        }
        List<Long> phraseIdList = hotQuestionConfigBo.getItemList().stream()
                .filter(item -> HotQuestionConfigTypeEnum.needQueryPhrase(item.getQuestionType()))
                .map(HotQuestionConfigItem::getPhraseId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<PhraseModel> phraseModelList = phraseRepository.findByIdList(phraseIdList);
        if (CollectionUtils.isEmpty(phraseModelList)) {
            return hotQuestionConfigBo;
        }
        Map<Long, PhraseModel> phraseModelMap = new HashMap<>();
        for (PhraseModel phraseModel : phraseModelList) {
            phraseModelMap.put(phraseModel.getId(), phraseModel);
        }
        hotQuestionConfigBo.getItemList().stream()
                .filter(item -> HotQuestionConfigTypeEnum.needQueryPhrase(item.getQuestionType()))
                .forEach(item -> item.setContent(phraseModelMap.get(item.getPhraseId()).getPhrase()));
        return hotQuestionConfigBo;
    }

    @Override
    public void saveConfigByAssistantBiz(HotQuestionConfigSaveRequestBo requestBo) {
        //检查入参
        ParamCheckUtil.notNull(requestBo, "非法入参");
        ParamCheckUtil.isTrue(crmBizIdAssistantBizNameMap.containsValue(requestBo.getAssistantBiz()), "非法业务线");
        ConfigRepository config = Lion.getConfigRepository(MdpContextUtils.getAppKey(), lionAccount, lionSecret);
        Map<String, HotQuestionConfigBo> currentConfigMap = config.getMap(HOT_QUESTION_CONFIG_LION_KEY, HotQuestionConfigBo.class);
        if (MapUtils.isEmpty(currentConfigMap)) {
            currentConfigMap = new HashMap<>();
        }
        //入参中itemList为空，则清空对应业务线高频问
        if (CollectionUtils.isEmpty(requestBo.getConfigItemList())) {
            currentConfigMap.put(requestBo.getAssistantBiz(), new HotQuestionConfigBo());
        } else if (!currentConfigMap.containsKey(requestBo.getAssistantBiz())) {
            //业务线当前没配置，则直接初始化
            currentConfigMap.put(requestBo.getAssistantBiz(),  new HotQuestionConfigBo(requestBo.getConfigItemList()));
        } else {
            HotQuestionConfigBo currentConfig = currentConfigMap.get(requestBo.getAssistantBiz());
            currentConfig.setItemList(requestBo.getConfigItemList());
            currentConfigMap.put(requestBo.getAssistantBiz(), currentConfig);
        }
        config.setValue(HOT_QUESTION_CONFIG_LION_KEY, JsonUtil.toJson(currentConfigMap));
    }

    @Override
    public List<HotQuestionBo> getHotQuestionList(Integer chooseBizId, SessionBo sessionBo) {
        String assistantBizName = getAssistantBizName(chooseBizId, sessionBo);
        HotQuestionConfigBo hotQuestionConfigBo = assistantHotQuestionMap.get(assistantBizName);
        if (hotQuestionConfigBo == null || CollectionUtils.isEmpty(hotQuestionConfigBo.getItemList())) {
            return Collections.emptyList();
        }
        return boToDto(hotQuestionConfigBo.getItemList(), sessionBo.getUid());
    }

    @Override
    public Set<Long> getFixedAnswerPhraseIdList(Integer chooseBizId, SessionBo sessionBo) {
        String assistantBizName = getAssistantBizName(chooseBizId, sessionBo);
        HotQuestionConfigBo hotQuestionConfigBo = assistantHotQuestionMap.get(assistantBizName);
        if (hotQuestionConfigBo == null || CollectionUtils.isEmpty(hotQuestionConfigBo.getItemList())) {
            return Collections.emptySet();
        }
        return hotQuestionConfigBo.getItemList().stream()
                .filter(item -> Objects.equals(item.getQuestionType(), HotQuestionConfigTypeEnum.FIXED_ANSWER.getCode()))
                .map(HotQuestionConfigItem::getPhraseId)
                .collect(Collectors.toSet());
    }


    private List<HotQuestionBo> boToDto(List<HotQuestionConfigItem> itemList, Integer uid) {
        List<HotQuestionBo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemList)) {
            return result;
        }
        // 收集需要查询的短语ID
        Set<Long> phraseIds = itemList.stream()
                .filter(item -> HotQuestionConfigTypeEnum.needQueryPhrase(item.getQuestionType()))
                .map(HotQuestionConfigItem::getPhraseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询短语
        Map<Long, PhraseModel> phraseModelMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(phraseIds)) {
            List<PhraseModel> phraseModels = phraseRepository.findByIdList(new ArrayList<>(phraseIds));
            if (!CollectionUtils.isEmpty(phraseModels)) {
                phraseModelMap = phraseModels.stream()
                        .collect(Collectors.toMap(PhraseModel::getId, phrase -> phrase, (p1, p2) -> p1));
            }
        }

        // 转换为BO对象
        for (HotQuestionConfigItem configItem : itemList) {
            if (Objects.nonNull(configItem)) {
                result.add(boToDto(configItem, uid, phraseModelMap));
            }
        }
        return result;
    }


    private HotQuestionBo boToDto(HotQuestionConfigItem itemBo, Integer uid, Map<Long, PhraseModel> finalPhraseModelMap) {
        HotQuestionBo bo = new HotQuestionBo();
        HotQuestionConfigTypeEnum configTypeEnum = HotQuestionConfigTypeEnum.findByCode(itemBo.getQuestionType());
        if (configTypeEnum == null) {
            return null;
        }
        bo.setAbilityType(configTypeEnum.getAbilityTypeEnum().getCode());
        bo.setOperationType(configTypeEnum.getOperationTypeEnum().getCode());
        //icon图案
        if (StringUtils.isNotBlank(itemBo.getIconUrl())) {
            bo.setLink(itemBo.getIconUrl());
        } else {
            bo.setLink(hotQuestionDefaultIconUrl);
        }
        switch (configTypeEnum) {
            case WORKFLOW:
            case FIXED_ANSWER:
                PhraseModel phraseModel = finalPhraseModelMap.get(itemBo.getPhraseId());
                if (phraseModel == null || StringUtils.isBlank(phraseModel.getStandardizedPhrase())) {
                    log.error("配置的标准问不存在或标准问法为空，itemBo={}", itemBo);
                    return null;
                }
                bo.setContent(phraseModel.getStandardizedPhrase());
                break;
            case RAG:
                bo.setContent(itemBo.getContent());
                break;
            case JUMP_LINK:
                bo.setContent(itemBo.getContent());
                //跳转链接
                bo.setUrl(itemBo.getJumpUrl());
                break;
            default:
                return null;
        }
        //高亮设置
        bo.setIsNew(isNew(itemBo, uid));
        return bo;
    }

    private Boolean isNew(HotQuestionConfigItem itemBo, Integer uid) {
        Boolean result = false;
        if (itemBo == null || StringUtils.isBlank(itemBo.getHighlightStartTime())) {
            return false;
        }
        try {
            Date highlightStartDate = DateUtil.parseDate(itemBo.getHighlightStartTime());
            Date highlightEndDate = DateUtil.parseDate(itemBo.getHighlightEndTime());
            Date now = new Date();
            if (highlightStartDate != null && highlightStartDate.after(now)) {
                //开始时间晚于当前，不是新问题
                return false;
            }
            if (highlightEndDate != null && highlightEndDate.before(now)) {
                //结束时间早于当前，不再是新问题
                return false;
            }
            // 开始-结束时间内是否有点击事件
            TraceLogQuery traceLogQuery = TraceLogQuery.builder().uid(uid)
                    .eventTypes(Collections.singletonList(TraceLogModel.EVENT_TRIGGER))
                    .contentLikeFormat(String.format("%%%s%%", itemBo.getContent()))
                    .startDate(highlightStartDate)
                    .endDate(highlightEndDate).entryPointLikeFormat("tab%").build();
            long triggerTimes = traceLogRepository.count(traceLogQuery);
            return triggerTimes == 0;
        } catch (RuntimeException e) {
            return result;
        }
    }


    public String getAssistantBizName(Integer chooseBizId, SessionBo sessionBo) {
        if (sessionBo == null) {
            log.error("热门问-获取所属业务线时，sessionBo");
            return null;
        }
        SessionSourceEntity sessionSourceEntity = sessionSourceConfigManager.getByCode(sessionBo.getSource());
        PlatformEnum platformEnum = PlatformEnum.getByCode(sessionSourceEntity.getPlatformCode());
        Integer finalBizId = null;
        if (PlatformEnum.APP.equals(platformEnum)) {
            if (chooseBizId == null) {
                log.error("热门问-app-获取所属业务线时，chooseBizId为空，sessionB={}", JsonUtil.toJson(sessionBo));
                return null;
            }
            //来源为蜜蜂app，获取用户当前选择的业务
            finalBizId = chooseBizId;
        } else {
            //来源为pc，获取登录人所在业务
            CrmPlatformUser crmPlatformUser = wmUserService.getCrmPlatformUser(sessionBo.getMis(), sessionBo.getUid());
            if (crmPlatformUser == null || CollectionUtils.isEmpty(crmPlatformUser.getBizIds())) {
                return null;
            } else if (crmPlatformUser.getBizIds().size() == 1) {
                finalBizId = crmPlatformUser.getBizIds().stream().findFirst().get();
            } else if (crmPlatformUser.getBizIds().contains(ZHIYING_BAI_LING_BIZ_ID)){
                //归属多个团队，且归属直营白领，则取直营白领
                finalBizId = ZHIYING_BAI_LING_BIZ_ID;
            } else {
                //归属多个团队，不归属直营白领，则取第一个命中的
                finalBizId = crmPlatformUser.getBizIds().stream()
                        .filter(bizId -> crmBizIdAssistantBizNameMap.containsKey(bizId))
                        .findFirst()
                        .orElseGet(() -> {
                            log.error("预期外的人员-业务归属，{}", JsonUtil.toJson(crmPlatformUser));
                            return null;
                        });
            }
        }
        return crmBizIdAssistantBizNameMap.get(finalBizId);
    }
}
