package com.sankuai.wmbdaiassistant.domain.enums.helpbd;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 摩西评论的类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-01 15:03
 */
@Getter
public enum MosesCommentTypeEnum {

    SUBMIT("submit", "提交"),
    CLOSE("close", "关闭"),
    ;

    private String code;
    private String desc;

    MosesCommentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MosesCommentTypeEnum getByCode(String code) {
        for (MosesCommentTypeEnum mosesCommentTypeEnum : values()) {
            if (StringUtils.equalsIgnoreCase(mosesCommentTypeEnum.getCode(), code)) {
                return mosesCommentTypeEnum;
            }
        }
        return null;
    }
}
