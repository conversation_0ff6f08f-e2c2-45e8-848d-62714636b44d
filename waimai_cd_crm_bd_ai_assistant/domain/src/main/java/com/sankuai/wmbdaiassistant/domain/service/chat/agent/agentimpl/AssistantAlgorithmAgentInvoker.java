package com.sankuai.wmbdaiassistant.domain.service.chat.agent.agentimpl;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.domain.bo.*;
import com.sankuai.wmbdaiassistant.domain.enums.AnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.ChatAnswerStatusEnum;
import com.sankuai.wmbdaiassistant.domain.model.BizAgentModel;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.AiChatConfig;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import com.sankuai.wmbdaiassistant.domain.service.chat.agent.BaseAgentInvoker;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.AlgorithmChatService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import com.sankuai.wmbdaiassistant.domain.service.chat.dataset.DatasetAuthService;
import com.sankuai.wmbdaiassistant.domain.service.chat.rag.impl.DefaultFragmentLlmRag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("assistantAlgorithm")
public class AssistantAlgorithmAgentInvoker extends DefaultFragmentLlmRag implements BaseAgentInvoker  {
    @Resource
    protected DatasetAuthService datasetAuthService;

    @Resource
    protected ChatRecordService chatRecordService;

    @Resource
    protected AiChatConfig aiChatConfig;

    @Resource
    protected ChatContentConverter chatContentConverter;

    @Resource
    private AlgorithmChatService algorithmChatService;

    @Resource
    private FragmentRepository fragmentRepository;

    
    @Override
    public void invoke(AgentInvokeBo agentInvokeBo, BizAgentModel bizAgentModel, GeneralCallback callback) {

        List<Long> datasetIds = datasetAuthService.queryUserVisibleDataset(agentInvokeBo.getSessionBo().getUid());
        List<ChatRecordBo> history = chatRecordService.fetchSessionHistory(agentInvokeBo.getSessionBo().getSessionId());

        log.info("invokeSpecifiedAgent stream,sessionId:{}", agentInvokeBo.getSessionBo().getSessionId());

        algorithmChatService.invokeSpecifiedAgent(bizAgentModel, agentInvokeBo.getSessionBo().getMis(),
                agentInvokeBo.getUserQuery(), agentInvokeBo.getMsgId(), datasetIds, history, response->{

                    log.info("invokeSpecifiedAgent stream response info, sessionId:{}, msgId:{}, response:{}", agentInvokeBo.getSessionBo().getSessionId(),agentInvokeBo.getMsgId(),response);

                    JsonNode jsonNode = JsonUtil.fromJson(response);
                    ParamCheckUtil.notNull(jsonNode, BizErrorEnum.ALGO_CHAT_API_ERROR, "算法聊天接口错误,响应结果异常");
                    int code = jsonNode.get("code").asInt();
                    String message = jsonNode.get("message").asText();
                    ParamCheckUtil.isTrue(code == 0, BizErrorEnum.ALGO_CHAT_API_ERROR,
                            String.format("算法聊天接口错误,code=%s,msg=%s", code, message));
                    
                    ChatStreamChunkBo streamChunkBo = JsonUtil.fromJsonNode(jsonNode.get("data"), ChatStreamChunkBo.class);

                    List<FragmentModel> fragmentModelList = new ArrayList<>();
                    boolean isFinished = streamChunkBo.isFinished();
                    String content = streamChunkBo.getAnswer();

                    if (isFinished) {
                        fragmentModelList
                                .addAll(fragmentRepository.findByIdList(DefaultUtil.defaultList(streamChunkBo.getReferences())
                                        .stream().map(FragmentReferenceBo::getId).collect(Collectors.toList())));
                        agentInvokeBo.getSessionBo().configFragments(fragmentModelList);
                    }

                    GeneralAnswerBo answer = new GeneralAnswerBo();
                    answer.setMsgId(agentInvokeBo.getMsgId());
                    answer.setStatus(
                            isFinished ? ChatAnswerStatusEnum.FINISH.getCode() : ChatAnswerStatusEnum.ANSWERING.getCode());
                    answer.setAnswerType(AnswerTypeEnum.FRAGMENT_REARRANGE.getCode());
                    answer.setAnswer(buildResponseMessage(content, isFinished, fragmentModelList, agentInvokeBo.getSessionBo().getSource()));
                    callback.answerCallback(answer);
                }, error -> {
                    log.error("invokeSpecifiedAgent stream,sessionId:{}, msgId:{}, response error for user {}: {}",
                            agentInvokeBo.getSessionBo().getSessionId(),
                            agentInvokeBo.getMsgId(),
                            agentInvokeBo.getSessionBo().getMis(),
                            error.getMessage(),
                            error);

                    GeneralAnswerBo generalAnswerBo = new GeneralAnswerBo();
                    generalAnswerBo.setAnswerType(AnswerTypeEnum.SYSTEM.getCode());
                    generalAnswerBo.setMsgId(agentInvokeBo.getMsgId());
                    generalAnswerBo.setAnswer(aiChatConfig.aiChatDefaultMsg);
                    generalAnswerBo.setStatus(ChatAnswerStatusEnum.FINISH.getCode());
                    callback.answerCallback(generalAnswerBo);
                });
    }
}


