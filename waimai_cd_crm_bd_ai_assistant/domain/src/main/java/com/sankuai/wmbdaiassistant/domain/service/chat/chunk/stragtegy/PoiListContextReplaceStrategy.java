package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.SessionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.wmbdaiassistant.domain.constant.PoiSelectorConstant.SYSTEM;


/**
 * poi列表存入seesionBo.extra
 */
@Slf4j
@Component
public class PoiListContextReplaceStrategy implements ChunkPatternReplaceStrategy {
    private static final String POI_MODE = "poi_list_context";
    private static final String POI_LIST = "poiList";
    @Resource
    private SessionCacheService sessionCacheService;

    @Override
    public boolean match(String mode) {
        return StringUtils.isNotBlank(mode) && mode.startsWith(POI_MODE);
    }

    @Override
    public String replace(String mode, String value, SessionBo sessionBo) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        List<Long> poiList = Arrays.stream(value.split(",")).map(Long::valueOf).collect(Collectors.toList());
        sessionBo.putExtraNestedValue(SYSTEM, POI_LIST, poiList);
        sessionCacheService.saveOrUpdate(sessionBo);
        return "";
    }

    /**
     * 获取追加文本后缀
     *
     * @param sessionBo
     * @return
     */
    @Override
    public String getAddChunkSuffix(SessionBo sessionBo) {
        return "";
    }
}   