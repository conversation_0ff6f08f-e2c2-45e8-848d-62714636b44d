package com.sankuai.wmbdaiassistant.domain.service.chat.agent;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.bo.AgentInvokeBo;
import com.sankuai.wmbdaiassistant.domain.model.BizAgentModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.ability.general.GeneralCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class AgentInvokerFactory {

    @Autowired
    private Map<String, BaseAgentInvoker> agentInvokerMap;

    @MdpConfig("biz.agent.invoke.config:{}")
    protected HashMap<String, BizAgentModel> bizAgentInvokeConfig;


    public void invoke(AgentInvokeBo agentInvokeBo, GeneralCallback callback) {

        log.info("AgentInvokerFactory invoke agentInvokeBo:{}", JsonUtil.toJson(agentInvokeBo));

        BizAgentModel bizAgentModel = bizAgentInvokeConfig.get(agentInvokeBo.getAgentName());
        ParamCheckUtil.notNull(bizAgentModel, String.format("agent invoke config not fund, agentName:%s", agentInvokeBo.getAgentName()));

        BaseAgentInvoker agentInvoker = agentInvokerMap.get(bizAgentModel.getInvokerType());
        ParamCheckUtil.notNull(agentInvoker, String.format("agent invoke type not fund, agentName:%s", agentInvokeBo.getAgentName()));

        agentInvoker.invoke(agentInvokeBo, bizAgentModel, callback);
    }
}
