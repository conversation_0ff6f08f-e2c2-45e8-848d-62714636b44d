package com.sankuai.wmbdaiassistant.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class IntentRecognitionRespData {
    /**
     * 类型 @see com.sankuai.wmbdaiassistant.enums.IntentRecognitionTypeEnum
     */
    @JsonProperty("intent_type")
    private String intentType;
    /**
     * 意图引导的每一帧的文字内容，返回给user
     */
    private String content;
    /**
     * agent名称，如果有intent_type=agent时有值
     */
    @JsonProperty("agent_name")
    private String agentName;
    /**
     * 需要触发的前端组件tag
     */
    @JsonProperty("ui_tag")
    private String tags;
    /**
     * 是否结束标识
     */
    @JsonProperty("is_finished")
    private boolean finish;
    /**
     * 任务流名称
     */
    @JsonProperty("workflow_name")
    private String workflowName;
    /**
     * 任务流参数或者qa agent的参数
     */
    private Object param;
    /**
     * 候选意图列表
     */
    @JsonProperty("candidate_intent")
    private List<String> candidateIntent;
}
