package com.sankuai.wmbdaiassistant.domain.model.vector;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 向量匹配结果模型
 *
 * <AUTHOR>
 * @description 封装向量相似度匹配的结果信息
 * @create 2024/12/24 10:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorMatchResultModel {

    /**
     * 匹配分数，范围通常为0-1，分数越高表示相似度越高
     */
    private Double matchScore;

    /**
     * 最终选择的字符串
     */
    private String selectedPhrase;

    /**
     * 是否找到匹配结果
     *
     * @return true表示找到匹配结果，false表示未找到
     */
    public boolean hasMatch() {
        return selectedPhrase != null && matchScore != null && matchScore > 0;
    }

    public static VectorMatchResultModel buildResult(String selectedPhrase, Double matchScore) {
        return VectorMatchResultModel.builder().selectedPhrase(selectedPhrase).matchScore(matchScore).build();
    }
}
