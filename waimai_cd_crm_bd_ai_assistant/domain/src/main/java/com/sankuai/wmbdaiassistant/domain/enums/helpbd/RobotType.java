package com.sankuai.wmbdaiassistant.domain.enums.helpbd;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-06-26
 * 机器人类型
 */
@Getter
@AllArgsConstructor
public enum RobotType {

    MOSES(0, "摩西机器人"),
    AI(1, "智能客服"),
    BD_AI_ASSISTANT(2, "销售智能助手"),
    ;

    private Integer code;

    private String name;

    /**
     * 是否是有效的机器人类型
     */
    public static boolean isValid(Integer robotType) {
        for (RobotType value : values()) {
            if (Objects.equals(value.getCode(), robotType)) {
                return true;
            }
        }
        return false;
    }
}
