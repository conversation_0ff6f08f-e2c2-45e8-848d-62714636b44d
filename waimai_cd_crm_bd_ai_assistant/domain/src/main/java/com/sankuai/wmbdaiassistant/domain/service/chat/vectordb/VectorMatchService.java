package com.sankuai.wmbdaiassistant.domain.service.chat.vectordb;

import java.util.List;

import com.sankuai.wmbdaiassistant.domain.model.vector.VectorMatchResultModel;

/**
 * 向量匹配服务
 *
 * <AUTHOR>
 * @description 向量相似度匹配服务
 * @create 2024/12/24 10:30
 */
public interface VectorMatchService {

    /**
     * 找到与输入文本最相似的短语
     *
     * @param input      输入文本
     * @param phraseList 候选短语列表
     * @return 匹配结果模型，包含匹配分数和最终选择的字符串；如果没有找到匹配则返回空结果
     */
    VectorMatchResultModel findMostSimilarPhrase(String input, List<String> phraseList);
}
