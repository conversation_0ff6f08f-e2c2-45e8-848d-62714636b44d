package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.FragmentCoreModel;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentCoreQuery;

import java.util.List;

/**
 * Fragment核心能力仓储接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-27 16:45
 */
public interface FragmentCoreRepository {

    /**
     * 根据查询条件查询
     *
     * @param fragmentQuery 查询条件
     * @return
     */
    List<FragmentCoreModel> findByQuery(FragmentCoreQuery fragmentQuery);

    /**
     * 批量修改（不存在，则添加，存在，则修改）
     *
     * @param datasetId 数据集ID
     * @param fragmentCoreModelList 分片列表
     */
    void batchUpsert(Long datasetId, List<FragmentCoreModel> fragmentCoreModelList);

    /**
     * 批量删除
     *
     * @param datasetId 数据集ID
     * @param idList 分片ID列表
     */
    void batchDelete(Long datasetId, List<String> idList);

}
