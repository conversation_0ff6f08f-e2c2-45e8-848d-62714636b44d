package com.sankuai.wmbdaiassistant.domain.service.chat.chunk.stragtegy;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.constant.PoiSelectorConstant;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.SessionCacheService;
import com.sankuai.wmbdaiassistant.domain.service.chat.content.ChatContentConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新签攻克商家多选标识存入seesionBo.extra
 */
@Slf4j
@Component
public class SkillContextReplaceStrategy implements ChunkPatternReplaceStrategy {
    @Resource
    private SessionCacheService sessionCacheService;
    @Resource
    private ChatContentConverter chatContentConverter;

    private static final String POI_MODE = "skill_context";
    private static final String NEW_POI_WEIGHT = "new_poi_weight";
    private static final String NOT_NEW_POI_WEIGHT = "not_new_poi_weight";
    private static final String NEW_POI_WEIGHT_TAG = "NewPoiWeight";
    private static final String NEW_SIGN_POI_CONQUER = "new_sign_poi_conquer";
    private static final String NOT_NEW_SIGN_POI_CONQUER = "not_new_sign_poi_conquer";
    private static final String NEW_SIGN_POI_CONQUER_TAG = "NewSignPoiConquer";
    private static final String ADD_SUFFIX_TAG_REGEX = "\"system\"\\s*:\\s*\\{[^}]*\"addSuffixTags\"\\s*:\\s*(\\[[^]]*\\])";
    @MdpConfig("friday.chunk.suffix:{}")
    public HashMap<String, Object> chunkSuffixMap;

    @Override
    public boolean match(String mode) {
        return StringUtils.isNotBlank(mode) && mode.startsWith(POI_MODE);
    }

    @Override
    public String replace(String mode, String value, SessionBo sessionBo) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        if (NEW_POI_WEIGHT.equals(value)) {
            sessionBo.putExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.ADD_SUFFIX_TAGS, Lists.newArrayList(NEW_POI_WEIGHT_TAG));
            sessionCacheService.saveOrUpdate(sessionBo);
        }
        if (NOT_NEW_POI_WEIGHT.equals(value)) {
            sessionBo.removeExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.ADD_SUFFIX_TAGS);
            sessionBo.removeExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.POI_LIST);
            sessionCacheService.saveOrUpdate(sessionBo);
        }
        if (NEW_SIGN_POI_CONQUER.equals(value)) {
            sessionBo.putExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.ADD_SUFFIX_TAGS, Lists.newArrayList(NEW_SIGN_POI_CONQUER_TAG));
            sessionCacheService.saveOrUpdate(sessionBo);
        }
        if (NOT_NEW_SIGN_POI_CONQUER.equals(value)) {
            sessionBo.removeExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.ADD_SUFFIX_TAGS);
            sessionBo.removeExtraNestedValue(PoiSelectorConstant.SYSTEM, PoiSelectorConstant.POI_LIST);
            sessionCacheService.saveOrUpdate(sessionBo);
        }
        return "";
    }

    /**
     * 获取追加文本后缀
     *
     * @param sessionBo
     * @return
     */
    @Override
    @SuppressWarnings("unchecked")
    public String getAddChunkSuffix(SessionBo sessionBo) {

        Map<String, Object> extraMap = sessionBo.getExtraMap();
        List<String> supportSuffix = sessionBo.currentContext().getAddSuffixTags();
        if (MapUtils.isEmpty(extraMap) || CollectionUtils.isEmpty(supportSuffix)) {
            return "";
        }
        Object object = extraMap.get(PoiSelectorConstant.SYSTEM);
        if (!(object instanceof Map)) {
            return "";
        }
        Map<String, Object> systemMap = (Map) object;
        if (MapUtils.isEmpty(systemMap)) {
            return "";
        }
        Object addSuffixTagsObj = systemMap.get(PoiSelectorConstant.ADD_SUFFIX_TAGS);
        if (!(addSuffixTagsObj instanceof List)) {
            return "";
        }
        List<String> addSuffixTags = (List<String>) addSuffixTagsObj;
        if (CollectionUtils.isEmpty(addSuffixTags)) {
            return "";
        }

        String suffix = "";
        for (String tag : addSuffixTags) {
            suffix = chatContentConverter.mergeList(suffix, JsonUtil.toJson(chunkSuffixMap.get(tag)));
        }
        return suffix;
    }
}   