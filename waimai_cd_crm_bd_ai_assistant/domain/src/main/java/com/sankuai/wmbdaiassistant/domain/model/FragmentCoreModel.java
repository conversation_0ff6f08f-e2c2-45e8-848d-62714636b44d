package com.sankuai.wmbdaiassistant.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Fragment 核心模型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-26 17:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentCoreModel {

    public static final String ID = "id";
    public static final String ID_KEYWORD = "id.keyword";
    public static final String TITLE = "title";
    public static final String TITLE_KEYWORD = "title.keyword";
    public static final String TAGS = "tags";
    public static final String TAGS_KEYWORD = "tags.keyword";
    public static final String CONTENT = "content";
    public static final String CONTENT_KEYWORD = "content.keyword";
    public static final String MERGE_CONTENT = "merge_content";
    public static final String MERGE_CONTENT_KEYWORD = "merge_content.keyword";
    public static final String CHUNK_ID = "chunk_id";
    public static final String WIKI_ID = "wiki_id";
    public static final String SCORE = "_score";

    /**
     * ES 主键 规则为: {wikiId}-{batchId}-{chunkId}
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 合并内容
     */
    private String mergeContent;

    /**
     * wiki ID
     */
    private Long wikiId;

    /**
     * 分片ID
     */
    private Integer chunkId;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 相似度评分
     */
    private Double score;

    /**
     * 将 FragmentModel 转换为 FragmentCoreModel
     * @return FragmentCoreModel 对象
     */
    public static FragmentCoreModel from(FragmentModel fragmentModel) {
        return FragmentCoreModel.builder()
                .id(fragmentModel.getId())
                .title(fragmentModel.getTitle())
                .content(fragmentModel.getContent())
                .mergeContent(String.format("'chunk title: %s\nchunk content: %s"
                        , fragmentModel.getTitle(), fragmentModel.getContent()))
                .wikiId(fragmentModel.getWikiId())
                .chunkId(fragmentModel.getChunkId())
                .tags(fragmentModel.getTags())
                .build();
    }

    /**
     * 转换为ES数据MAP
     * @return
     */
    public Map<String, Object> toMap() {
        Map<String, Object> itemData = new HashMap<>();
        itemData.put(FragmentCoreModel.ID, this.getId());
        itemData.put(FragmentCoreModel.WIKI_ID, this.getWikiId());
        itemData.put(FragmentCoreModel.CHUNK_ID, this.getChunkId());
        itemData.put(FragmentCoreModel.TITLE, this.getTitle());
        itemData.put(FragmentCoreModel.CONTENT, this.getContent());
        itemData.put(FragmentCoreModel.MERGE_CONTENT, this.getMergeContent());
        if (CollectionUtils.isNotEmpty(this.getTags())) {
            itemData.put(FragmentCoreModel.TAGS, String.join(",", this.getTags()));
        } else {
            itemData.put(FragmentCoreModel.TAGS, "");
        }
        return itemData;
    }

    /**
     * 重写 equals 方法，用于比较两个 FragmentCoreModel 对象是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FragmentCoreModel that = (FragmentCoreModel) o;

        if (!Objects.equals(id, that.id)) return false;
        if (!Objects.equals(title, that.title)) return false;
        if (!Objects.equals(content, that.content)) return false;
        if (!Objects.equals(mergeContent, that.mergeContent)) return false;
        if (!Objects.equals(wikiId, that.wikiId)) return false;
        if (!Objects.equals(chunkId, that.chunkId)) return false;
        return !Objects.equals(tags, that.tags);
    }

    /**
     * 重写 hashCode 方法，确保相等的对象具有相同的哈希码
     */
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (content != null ? content.hashCode() : 0);
        result = 31 * result + (mergeContent != null ? mergeContent.hashCode() : 0);
        result = 31 * result + (wikiId != null ? wikiId.hashCode() : 0);
        result = 31 * result + (chunkId != null ? chunkId.hashCode() : 0);
        result = 31 * result + (tags != null ? tags.hashCode() : 0);
        return result;
    }

}
