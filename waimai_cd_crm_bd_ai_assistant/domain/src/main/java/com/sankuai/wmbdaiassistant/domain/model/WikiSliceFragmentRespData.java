package com.sankuai.wmbdaiassistant.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * wiki分片服务，由于算法侧未提供响应的具体模型，因此自定义模型承载wiki分片请求响应结果
 */
@Getter
@Setter
public class WikiSliceFragmentRespData {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private List<AlgorithmFragmentModel> data;
}
