package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 任务类型枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/07/08
 */
@Getter
public enum JobTypeEnum {
    POI_DIAGNOSIS("PoiDiagnosis", "商家诊断");

    private String code;
    private String desc;

    JobTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static JobTypeEnum getByCode(String code) {
        for (JobTypeEnum type : JobTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
