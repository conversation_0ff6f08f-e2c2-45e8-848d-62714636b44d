package com.sankuai.wmbdaiassistant.domain.repository;

import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;

import java.util.Date;
import java.util.List;

/**
 * jobItem仓储
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
public interface JobItemRepository {

    /**
     * 根据jobId查询jobItem列表
     * @param jobId
     * @return
     */
    List<JobItemModel> findByJobId(Long jobId);

    /**
     * 根据任务type和status查询jobItem列表
     * @param type
     * @param status
     * @return
     */
    List<JobItemModel> findByTypeAndStatus(String type, String status);

    /**
     * 根据jobIds和status查询jobItem数量
     * @param jobIds
     * @param status
     * @return
     */
    Long countByJobIdsAndStatus(List<Long> jobIds, String status);

    /**
     * 插入jobItem
     * @param jobItemModel
     * @return
     */
    Long insert(JobItemModel jobItemModel);


    /**
     * 更新jobItem
     * @param jobItemModel 待更新的jobItem模型
     * @return 是否更新成功
     */
    Boolean update(JobItemModel jobItemModel);

    /**
     * 根据jobIds查询最新被更新的jobItem的更新时间
     *
     * @param jobIds
     * @return
     */
    Date findLatestUpdatedDateByJobIds(List<Long> jobIds);
}
