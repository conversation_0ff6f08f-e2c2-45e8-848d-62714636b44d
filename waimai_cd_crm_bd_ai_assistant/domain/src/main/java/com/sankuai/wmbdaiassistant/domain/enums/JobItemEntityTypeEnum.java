package com.sankuai.wmbdaiassistant.domain.enums;

import lombok.Getter;

/**
 * 任务项实体类型枚举
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/07/15
 */
@Getter
public enum JobItemEntityTypeEnum {
    WM_POI_ID("wmPoiId", "外卖商家id");

    private String code;
    private String desc;

    JobItemEntityTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static JobItemEntityTypeEnum getByCode(String code) {
        for (JobItemEntityTypeEnum entityTypeEnum : JobItemEntityTypeEnum.values()) {
            if (entityTypeEnum.getCode().equals(code)) {
                return entityTypeEnum;
            }
        }
        return null;
    }
}
