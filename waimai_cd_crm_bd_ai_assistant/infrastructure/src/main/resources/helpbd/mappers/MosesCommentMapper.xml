<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.MosesCommentMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_name" jdbcType="VARCHAR" property="bizName" />
    <result column="robot_type" jdbcType="INTEGER" property="robotType" />
    <result column="robot_id" jdbcType="VARCHAR" property="robotId" />
    <result column="star" jdbcType="INTEGER" property="star" />
    <result column="value" jdbcType="INTEGER" property="value" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="tips" jdbcType="VARCHAR" property="tips" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    <result column="comment" jdbcType="LONGVARCHAR" property="comment" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mis_id, `uid`, `type`, tenant_id, tenant_name, biz_id, biz_name, robot_type, 
    robot_id, star, `value`, `desc`, tips, ctime
  </sql>
  <sql id="Blob_Column_List">
    `comment`
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bd_help_moses_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bd_help_moses_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bd_help_moses_comment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bd_help_moses_comment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample">
    delete from bd_help_moses_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_help_moses_comment (mis_id, `uid`, `type`, 
      tenant_id, tenant_name, biz_id, 
      biz_name, robot_type, robot_id, 
      star, `value`, `desc`, 
      tips, ctime, `comment`
      )
    values (#{misId,jdbcType=VARCHAR}, #{uid,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{tenantName,jdbcType=VARCHAR}, #{bizId,jdbcType=BIGINT}, 
      #{bizName,jdbcType=VARCHAR}, #{robotType,jdbcType=INTEGER}, #{robotId,jdbcType=VARCHAR}, 
      #{star,jdbcType=INTEGER}, #{value,jdbcType=INTEGER}, #{desc,jdbcType=VARCHAR}, 
      #{tips,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{comment,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_help_moses_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="misId != null">
        mis_id,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizName != null">
        biz_name,
      </if>
      <if test="robotType != null">
        robot_type,
      </if>
      <if test="robotId != null">
        robot_id,
      </if>
      <if test="star != null">
        star,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="tips != null">
        tips,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="misId != null">
        #{misId,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizName != null">
        #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        #{robotType,jdbcType=INTEGER},
      </if>
      <if test="robotId != null">
        #{robotId,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        #{star,jdbcType=INTEGER},
      </if>
      <if test="value != null">
        #{value,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="tips != null">
        #{tips,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample" resultType="java.lang.Long">
    select count(*) from bd_help_moses_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bd_help_moses_comment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.misId != null">
        mis_id = #{record.misId,jdbcType=VARCHAR},
      </if>
      <if test="record.uid != null">
        `uid` = #{record.uid,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantName != null">
        tenant_name = #{record.tenantName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.bizName != null">
        biz_name = #{record.bizName,jdbcType=VARCHAR},
      </if>
      <if test="record.robotType != null">
        robot_type = #{record.robotType,jdbcType=INTEGER},
      </if>
      <if test="record.robotId != null">
        robot_id = #{record.robotId,jdbcType=VARCHAR},
      </if>
      <if test="record.star != null">
        star = #{record.star,jdbcType=INTEGER},
      </if>
      <if test="record.value != null">
        `value` = #{record.value,jdbcType=INTEGER},
      </if>
      <if test="record.desc != null">
        `desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.tips != null">
        tips = #{record.tips,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update bd_help_moses_comment
    set id = #{record.id,jdbcType=BIGINT},
      mis_id = #{record.misId,jdbcType=VARCHAR},
      `uid` = #{record.uid,jdbcType=BIGINT},
      `type` = #{record.type,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      tenant_name = #{record.tenantName,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_name = #{record.bizName,jdbcType=VARCHAR},
      robot_type = #{record.robotType,jdbcType=INTEGER},
      robot_id = #{record.robotId,jdbcType=VARCHAR},
      star = #{record.star,jdbcType=INTEGER},
      `value` = #{record.value,jdbcType=INTEGER},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      tips = #{record.tips,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      `comment` = #{record.comment,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bd_help_moses_comment
    set id = #{record.id,jdbcType=BIGINT},
      mis_id = #{record.misId,jdbcType=VARCHAR},
      `uid` = #{record.uid,jdbcType=BIGINT},
      `type` = #{record.type,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      tenant_name = #{record.tenantName,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_name = #{record.bizName,jdbcType=VARCHAR},
      robot_type = #{record.robotType,jdbcType=INTEGER},
      robot_id = #{record.robotId,jdbcType=VARCHAR},
      star = #{record.star,jdbcType=INTEGER},
      `value` = #{record.value,jdbcType=INTEGER},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      tips = #{record.tips,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    update bd_help_moses_comment
    <set>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizName != null">
        biz_name = #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        robot_type = #{robotType,jdbcType=INTEGER},
      </if>
      <if test="robotId != null">
        robot_id = #{robotId,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        star = #{star,jdbcType=INTEGER},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="tips != null">
        tips = #{tips,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    update bd_help_moses_comment
    set mis_id = #{misId,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=BIGINT},
      `type` = #{type,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_name = #{bizName,jdbcType=VARCHAR},
      robot_type = #{robotType,jdbcType=INTEGER},
      robot_id = #{robotId,jdbcType=VARCHAR},
      star = #{star,jdbcType=INTEGER},
      `value` = #{value,jdbcType=INTEGER},
      `desc` = #{desc,jdbcType=VARCHAR},
      tips = #{tips,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      `comment` = #{comment,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo">
    update bd_help_moses_comment
    set mis_id = #{misId,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=BIGINT},
      `type` = #{type,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_name = #{bizName,jdbcType=VARCHAR},
      robot_type = #{robotType,jdbcType=INTEGER},
      robot_id = #{robotId,jdbcType=VARCHAR},
      star = #{star,jdbcType=INTEGER},
      `value` = #{value,jdbcType=INTEGER},
      `desc` = #{desc,jdbcType=VARCHAR},
      tips = #{tips,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bd_help_moses_comment
    (mis_id, `uid`, `type`, tenant_id, tenant_name, biz_id, biz_name, robot_type, robot_id, 
      star, `value`, `desc`, tips, ctime, `comment`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.misId,jdbcType=VARCHAR}, #{item.uid,jdbcType=BIGINT}, #{item.type,jdbcType=VARCHAR}, 
        #{item.tenantId,jdbcType=BIGINT}, #{item.tenantName,jdbcType=VARCHAR}, #{item.bizId,jdbcType=BIGINT}, 
        #{item.bizName,jdbcType=VARCHAR}, #{item.robotType,jdbcType=INTEGER}, #{item.robotId,jdbcType=VARCHAR}, 
        #{item.star,jdbcType=INTEGER}, #{item.value,jdbcType=INTEGER}, #{item.desc,jdbcType=VARCHAR}, 
        #{item.tips,jdbcType=VARCHAR}, #{item.ctime,jdbcType=TIMESTAMP}, #{item.comment,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>