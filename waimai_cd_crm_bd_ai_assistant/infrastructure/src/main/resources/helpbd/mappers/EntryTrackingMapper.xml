<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.EntryTrackingMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_name" jdbcType="VARCHAR" property="bizName" />
    <result column="robot_type" jdbcType="INTEGER" property="robotType" />
    <result column="robot_id" jdbcType="VARCHAR" property="robotId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mis_id, `uid`, `source`, tenant_id, tenant_name, biz_id, biz_name, robot_type, 
    robot_id, ctime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.EntryTrackingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bd_help_entry_tracking
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bd_help_entry_tracking
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bd_help_entry_tracking
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.EntryTrackingPoExample">
    delete from bd_help_entry_tracking
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_help_entry_tracking (mis_id, `uid`, `source`, 
      tenant_id, tenant_name, biz_id, 
      biz_name, robot_type, robot_id, 
      ctime)
    values (#{misId,jdbcType=VARCHAR}, #{uid,jdbcType=BIGINT}, #{source,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{tenantName,jdbcType=VARCHAR}, #{bizId,jdbcType=BIGINT}, 
      #{bizName,jdbcType=VARCHAR}, #{robotType,jdbcType=INTEGER}, #{robotId,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_help_entry_tracking
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="misId != null">
        mis_id,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizName != null">
        biz_name,
      </if>
      <if test="robotType != null">
        robot_type,
      </if>
      <if test="robotId != null">
        robot_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="misId != null">
        #{misId,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizName != null">
        #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        #{robotType,jdbcType=INTEGER},
      </if>
      <if test="robotId != null">
        #{robotId,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.EntryTrackingPoExample" resultType="java.lang.Long">
    select count(*) from bd_help_entry_tracking
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bd_help_entry_tracking
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.misId != null">
        mis_id = #{record.misId,jdbcType=VARCHAR},
      </if>
      <if test="record.uid != null">
        `uid` = #{record.uid,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantName != null">
        tenant_name = #{record.tenantName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.bizName != null">
        biz_name = #{record.bizName,jdbcType=VARCHAR},
      </if>
      <if test="record.robotType != null">
        robot_type = #{record.robotType,jdbcType=INTEGER},
      </if>
      <if test="record.robotId != null">
        robot_id = #{record.robotId,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bd_help_entry_tracking
    set id = #{record.id,jdbcType=BIGINT},
      mis_id = #{record.misId,jdbcType=VARCHAR},
      `uid` = #{record.uid,jdbcType=BIGINT},
      `source` = #{record.source,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      tenant_name = #{record.tenantName,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_name = #{record.bizName,jdbcType=VARCHAR},
      robot_type = #{record.robotType,jdbcType=INTEGER},
      robot_id = #{record.robotId,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo">
    update bd_help_entry_tracking
    <set>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizName != null">
        biz_name = #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        robot_type = #{robotType,jdbcType=INTEGER},
      </if>
      <if test="robotId != null">
        robot_id = #{robotId,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo">
    update bd_help_entry_tracking
    set mis_id = #{misId,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=BIGINT},
      `source` = #{source,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_name = #{bizName,jdbcType=VARCHAR},
      robot_type = #{robotType,jdbcType=INTEGER},
      robot_id = #{robotId,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bd_help_entry_tracking
    (mis_id, `uid`, `source`, tenant_id, tenant_name, biz_id, biz_name, robot_type, robot_id, 
      ctime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.misId,jdbcType=VARCHAR}, #{item.uid,jdbcType=BIGINT}, #{item.source,jdbcType=VARCHAR}, 
        #{item.tenantId,jdbcType=BIGINT}, #{item.tenantName,jdbcType=VARCHAR}, #{item.bizId,jdbcType=BIGINT}, 
        #{item.bizName,jdbcType=VARCHAR}, #{item.robotType,jdbcType=INTEGER}, #{item.robotId,jdbcType=VARCHAR}, 
        #{item.ctime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>