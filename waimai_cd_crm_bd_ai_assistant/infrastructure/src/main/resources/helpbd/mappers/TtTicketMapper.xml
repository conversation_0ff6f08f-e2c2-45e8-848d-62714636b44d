<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.TtTicketDao">
    <resultMap id="BaseResultMap" type="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="ticket_id" jdbcType="INTEGER" property="ticketId"/>
        <result column="rg_id" jdbcType="INTEGER" property="rgId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="ones_id" jdbcType="VARCHAR" property="onesId"/>
        <result column="ones_state" jdbcType="VARCHAR" property="onesState"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="labels" jdbcType="VARCHAR" property="labels"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, ticket_id, rg_id, name, creator_mis, state, ones_id, ones_state, create_time, modify_time, version, labels
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_ticket
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByTicketId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_ticket
        where ticket_id = #{ticketId,jdbcType=INTEGER}
    </select>
    <select id="selectByOnesId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_ticket
        where ones_id = #{onesId,jdbcType=VARCHAR}
    </select>
    <select id="selectUnOnesRefByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tt_ticket
        where id > #{minId}
        and ones_id = ''
        and create_time >= #{startTime}
        order by id
        limit #{pageSize}
    </select>
    <insert id="insertOrUpdateSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tt_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">
                ticket_id,
            </if>
            <if test="rgId != null">
                rg_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="creatorMis != null">
                creator_mis,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="onesId != null">
                ones_id,
            </if>
            <if test="onesState != null">
                ones_state,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="labels != null">
                labels,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">
                #{ticketId,jdbcType=INTEGER},
            </if>
            <if test="rgId != null">
                #{rgId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="creatorMis != null">
                #{creatorMis,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="onesId != null">
                #{onesId,jdbcType=VARCHAR},
            </if>
            <if test="onesState != null">
                #{onesState,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=TIMESTAMP},
            </if>
            <if test="labels != null">
                #{labels,jdbcType=VARCHAR},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <if test="ticketId != null">
            ticket_id = #{ticketId,jdbcType=INTEGER},
        </if>
        <if test="rgId != null">
            rg_id = #{rgId,jdbcType=INTEGER},
        </if>
        <if test="name != null">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="creatorMis != null">
            creator_mis = #{creatorMis,jdbcType=VARCHAR},
        </if>
        <if test="state != null">
            state = #{state,jdbcType=VARCHAR},
        </if>
        <if test="onesId != null">
            ones_id = #{onesId,jdbcType=VARCHAR},
        </if>
        <if test="onesState != null">
            ones_state = #{onesState,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        <if test="labels != null">
            labels = #{labels,jdbcType=VARCHAR},
        </if>
        version = version + 1
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo">
        update tt_ticket set
        <if test="ticketId != null">
            ticket_id = #{ticketId,jdbcType=INTEGER},
        </if>
        <if test="rgId != null">
            rg_id = #{rgId,jdbcType=INTEGER},
        </if>
        <if test="name != null">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="creatorMis != null">
            creator_mis = #{creatorMis,jdbcType=VARCHAR},
        </if>
        <if test="state != null">
            state = #{state,jdbcType=VARCHAR},
        </if>
        <if test="onesId != null">
            ones_id = #{onesId,jdbcType=VARCHAR},
        </if>
        <if test="onesState != null">
            ones_state = #{onesState,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="labels != null">
            labels = #{labels,jdbcType=VARCHAR},
        </if>
        version = version + 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByTicketIdSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo">
        update tt_ticket set
        <if test="rgId != null">
            rg_id = #{rgId,jdbcType=INTEGER},
        </if>
        <if test="name != null">
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="creatorMis != null">
            creator_mis = #{creatorMis,jdbcType=VARCHAR},
        </if>
        <if test="state != null">
            state = #{state,jdbcType=VARCHAR},
        </if>
        <if test="onesId != null">
            ones_id = #{onesId,jdbcType=VARCHAR},
        </if>
        <if test="onesState != null">
            ones_state = #{onesState,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="labels != null">
            labels = #{labels,jdbcType=VARCHAR},
        </if>
        version = version + 1
        where ticket_id = #{ticketId,jdbcType=INTEGER}
    </update>
</mapper>