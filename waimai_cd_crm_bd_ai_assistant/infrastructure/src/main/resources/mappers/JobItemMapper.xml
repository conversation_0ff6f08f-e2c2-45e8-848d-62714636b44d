<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.wmbdaiassistant.infrastructure.dal.mapper.JobItemMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="mis" jdbcType="VARCHAR" property="mis" />
    <result column="entity_type" jdbcType="VARCHAR" property="entityType" />
    <result column="entity_id" jdbcType="VARCHAR" property="entityId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="trigger" jdbcType="VARCHAR" property="trigger" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, job_id, `type`, uid, mis, entity_type, entity_id, status, `trigger`, ctime, utime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bd_ai_assistant_job_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bd_ai_assistant_job_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bd_ai_assistant_job_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobItemExample">
    delete from bd_ai_assistant_job_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_ai_assistant_job_item (job_id, `type`, uid,
      mis, entity_type, entity_id, 
      status, `trigger`, ctime,
      utime)
    values (#{jobId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{uid,jdbcType=INTEGER}, 
      #{mis,jdbcType=VARCHAR}, #{entityType,jdbcType=VARCHAR}, #{entityId,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{trigger,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{utime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_ai_assistant_job_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jobId != null">
        job_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="mis != null">
        mis,
      </if>
      <if test="entityType != null">
        entity_type,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="trigger != null">
        `trigger`,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="utime != null">
        utime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jobId != null">
        #{jobId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=INTEGER},
      </if>
      <if test="mis != null">
        #{mis,jdbcType=VARCHAR},
      </if>
      <if test="entityType != null">
        #{entityType,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="trigger != null">
        #{trigger,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobItemExample" resultType="java.lang.Long">
    select count(*) from bd_ai_assistant_job_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bd_ai_assistant_job_item
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.jobId != null">
        job_id = #{row.jobId,jdbcType=BIGINT},
      </if>
      <if test="row.type != null">
        `type` = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.uid != null">
        uid = #{row.uid,jdbcType=INTEGER},
      </if>
      <if test="row.mis != null">
        mis = #{row.mis,jdbcType=VARCHAR},
      </if>
      <if test="row.entityType != null">
        entity_type = #{row.entityType,jdbcType=VARCHAR},
      </if>
      <if test="row.entityId != null">
        entity_id = #{row.entityId,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=VARCHAR},
      </if>
      <if test="row.trigger != null">
        `trigger` = #{row.trigger,jdbcType=VARCHAR},
      </if>
      <if test="row.ctime != null">
        ctime = #{row.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.utime != null">
        utime = #{row.utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bd_ai_assistant_job_item
    set id = #{row.id,jdbcType=BIGINT},
      job_id = #{row.jobId,jdbcType=BIGINT},
     `type` = #{row.type,jdbcType=VARCHAR},
      uid = #{row.uid,jdbcType=INTEGER},
      mis = #{row.mis,jdbcType=VARCHAR},
      entity_type = #{row.entityType,jdbcType=VARCHAR},
      entity_id = #{row.entityId,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=VARCHAR},
     `trigger` = #{row.trigger,jdbcType=VARCHAR},
      ctime = #{row.ctime,jdbcType=TIMESTAMP},
      utime = #{row.utime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem">
    update bd_ai_assistant_job_item
    <set>
      <if test="jobId != null">
        job_id = #{jobId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=INTEGER},
      </if>
      <if test="mis != null">
        mis = #{mis,jdbcType=VARCHAR},
      </if>
      <if test="entityType != null">
        entity_type = #{entityType,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="trigger != null">
        `trigger` = #{trigger,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem">
    update bd_ai_assistant_job_item
    set job_id = #{jobId,jdbcType=BIGINT},
       `type` = #{type,jdbcType=VARCHAR},
        uid = #{uid,jdbcType=INTEGER},
        mis = #{mis,jdbcType=VARCHAR},
        entity_type = #{entityType,jdbcType=VARCHAR},
        entity_id = #{entityId,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
       `trigger` = #{trigger,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      utime = #{utime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bd_ai_assistant_job_item
    (job_id, `type`, uid, mis, entity_type, entity_id, status, `trigger`, ctime, utime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.jobId,jdbcType=BIGINT}, #{item.type,jdbcType=VARCHAR}, #{item.uid,jdbcType=INTEGER}, 
        #{item.mis,jdbcType=VARCHAR}, #{item.entityType,jdbcType=VARCHAR}, #{item.entityId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.trigger,jdbcType=VARCHAR}, #{item.ctime,jdbcType=TIMESTAMP}, 
        #{item.utime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>