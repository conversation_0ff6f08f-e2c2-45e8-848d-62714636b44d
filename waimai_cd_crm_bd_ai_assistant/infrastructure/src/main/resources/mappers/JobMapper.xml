<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.wmbdaiassistant.infrastructure.dal.mapper.JobMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="mis" jdbcType="VARCHAR" property="mis" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `type`, uid, mis, ctime, utime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bd_ai_assistant_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bd_ai_assistant_job
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bd_ai_assistant_job
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobExample">
    delete from bd_ai_assistant_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_ai_assistant_job (`type`, uid, mis,
      ctime, utime)
    values (#{type,jdbcType=VARCHAR}, #{uid,jdbcType=INTEGER}, #{mis,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{utime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_ai_assistant_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="mis != null">
        mis,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="utime != null">
        utime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=INTEGER},
      </if>
      <if test="mis != null">
        #{mis,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobExample" resultType="java.lang.Long">
    select count(*) from bd_ai_assistant_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bd_ai_assistant_job
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.type != null">
        `type` = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.uid != null">
        uid = #{row.uid,jdbcType=INTEGER},
      </if>
      <if test="row.mis != null">
        mis = #{row.mis,jdbcType=VARCHAR},
      </if>
      <if test="row.ctime != null">
        ctime = #{row.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.utime != null">
        utime = #{row.utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bd_ai_assistant_job
    set id = #{row.id,jdbcType=BIGINT},
    `type` = #{row.type,jdbcType=VARCHAR},
      uid = #{row.uid,jdbcType=INTEGER},
      mis = #{row.mis,jdbcType=VARCHAR},
      ctime = #{row.ctime,jdbcType=TIMESTAMP},
      utime = #{row.utime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job">
    update bd_ai_assistant_job
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=INTEGER},
      </if>
      <if test="mis != null">
        mis = #{mis,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job">
    update bd_ai_assistant_job
    set `type` = #{type,jdbcType=VARCHAR},
      uid = #{uid,jdbcType=INTEGER},
      mis = #{mis,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      utime = #{utime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bd_ai_assistant_job
    (`type`, uid, mis, ctime, utime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.type,jdbcType=VARCHAR}, #{item.uid,jdbcType=INTEGER}, #{item.mis,jdbcType=VARCHAR}, 
        #{item.ctime,jdbcType=TIMESTAMP}, #{item.utime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>