package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 *
 *   表名: bd_help_entry_tracking
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class EntryTrackingPo {
    /**
     *   字段: id
     *   说明: 主键id
     */
    private Long id;

    /**
     *   字段: mis_id
     *   说明: 访问人的 misId
     */
    private String misId;

    /**
     *   字段: uid
     *   说明: 访问人的 uid
     */
    private Long uid;

    /**
     *   字段: source
     *   说明: 来源，BEE_MY_HLEP：通过蜜蜂上的在线提问，BEE_POI_DETAIL：通过蜜蜂上商家详情进入提问，DAXIANG_WORKSTATION_APP：通过大象工作台进入提问
     */
    private String source;

    /**
     *   字段: tenant_id
     *   说明: infra 的团队管理的租户ID
     */
    private Long tenantId;

    /**
     *   字段: tenant_name
     *   说明: infra 的团队管理的租户名称
     */
    private String tenantName;

    /**
     *   字段: biz_id
     *   说明: infra 的团队管理的业务ID
     */
    private Long bizId;

    /**
     *   字段: biz_name
     *   说明: infra 的团队管理的业务名字
     */
    private String bizName;

    /**
     *   字段: robot_type
     *   说明: 机器人类型，0: 摩西机器人 1: 智能客服
     */
    private Integer robotType;

    /**
     *   字段: robot_id
     *   说明: 摩西机器人ID/智能客服url
     */
    private String robotId;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;
}