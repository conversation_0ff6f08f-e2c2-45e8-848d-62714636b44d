package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MosesCommentMapper extends MybatisBLOBsMapper<MosesCommentPo, MosesCommentPoExample, Long> {
    int batchInsert(@Param("list") List<MosesCommentPo> list);
}