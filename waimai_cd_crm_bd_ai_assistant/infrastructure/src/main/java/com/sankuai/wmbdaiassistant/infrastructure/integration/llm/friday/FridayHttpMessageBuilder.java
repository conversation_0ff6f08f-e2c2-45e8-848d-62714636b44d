package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday;

import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.LlmTokenEnsurer;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request.FridayHttpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Friday HTTP API 消息构建器
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 15:30
 */
public class FridayHttpMessageBuilder {

    /**
     * 构建Friday HTTP API格式的消息列表
     *
     * @param prompt    系统提示词
     * @param history   历史对话记录
     * @param message   当前用户消息
     * @param maxLength 最大token长度
     * @return Friday HTTP API格式的消息列表
     */
    public static List<FridayHttpMessage> build(String prompt, List<ChatRecordBo> history, String message, int maxLength) {
        
        Triple<String, List<ChatRecordBo>, String> triple = LlmTokenEnsurer.ensure(prompt, history, message, maxLength);

        prompt = triple.getLeft();
        history = triple.getMiddle();
        message = triple.getRight();

        List<FridayHttpMessage> messageList = new ArrayList<>();
        
        // 添加系统提示词
        if (StringUtils.isNotBlank(prompt)) {
            messageList.add(new FridayHttpMessage("system", prompt));
        }
        
        // 添加历史对话记录
        if (CollectionUtils.isNotEmpty(history)) {
            messageList.addAll(history.stream()
                .filter(Objects::nonNull)
                .map(chatRecordBo -> {
                    ChatContentTypeEnum contentType = chatRecordBo.getType();
                    if (contentType == ChatContentTypeEnum.ANSWER) {
                        return new FridayHttpMessage("assistant", chatRecordBo.getContent());
                    }
                    if (contentType == ChatContentTypeEnum.QUESTION) {
                        return new FridayHttpMessage("user", chatRecordBo.getContent());
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        }

        // 添加当前用户消息
        if (StringUtils.isNotBlank(message)) {
            messageList.add(new FridayHttpMessage("user", message));
        }
        
        return messageList;
    }
}