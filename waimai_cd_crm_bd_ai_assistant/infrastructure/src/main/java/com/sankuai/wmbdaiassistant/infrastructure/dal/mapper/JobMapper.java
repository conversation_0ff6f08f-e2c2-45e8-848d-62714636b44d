package com.sankuai.wmbdaiassistant.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job;
import com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JobMapper extends MybatisBaseMapper<Job, JobExample, Long> {
    int batchInsert(@Param("list") List<Job> list);
}