package com.sankuai.wmbdaiassistant.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem;
import com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobItemExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JobItemMapper extends MybatisBaseMapper<JobItem, JobItemExample, Long> {
    int batchInsert(@Param("list") List<JobItem> list);
}