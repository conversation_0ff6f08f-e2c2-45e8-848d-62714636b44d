package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Friday HTTP API 工具调用信息类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpToolCalls {

    /**
     * 工具调用的唯一ID
     */
    private String id;

    /**
     * 工具调用的类型（如 function）
     */
    private String type;

    /**
     * 工具调用的索引
     */
    private Integer index;

    /**
     * 工具调用对应的函数信息
     */
    private FridayHttpToolCallsFunction function;

    /**
     * 代码相关信息（可选）
     */
    private Object code;

    /**
     * 检索相关信息（可选）
     */
    private Object retrieval;
}
