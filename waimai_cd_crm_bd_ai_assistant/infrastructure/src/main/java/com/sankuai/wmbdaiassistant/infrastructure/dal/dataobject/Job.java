package com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: bd_ai_assistant_job
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Job {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: type
     *   说明: 任务类型
     */
    private String type;

    /**
     *   字段: uid
     *   说明: 用户id
     */
    private Integer uid;

    /**
     *   字段: mis
     *   说明: 用户mis
     */
    private String mis;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;

    /**
     *   字段: utime
     *   说明: 更新时间
     */
    private Date utime;
}