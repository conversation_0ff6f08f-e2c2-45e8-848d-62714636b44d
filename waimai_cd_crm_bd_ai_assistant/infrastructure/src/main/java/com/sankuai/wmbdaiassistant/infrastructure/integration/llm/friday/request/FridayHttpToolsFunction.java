package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * Friday HTTP API 描述工具调用中的函数信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 11:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpToolsFunction {

    /**
     * 函数名称
    */
    @NonNull
    private String name;

    /**
     * 函数作用描述
     */
    private String description;

    /**
     * 函数所需参数
     */
    private FridayHttpToolsFunctionParameters parameters;
}