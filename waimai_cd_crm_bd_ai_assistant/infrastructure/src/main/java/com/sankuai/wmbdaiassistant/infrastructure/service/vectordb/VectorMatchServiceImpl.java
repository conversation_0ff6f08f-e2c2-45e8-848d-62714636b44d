package com.sankuai.wmbdaiassistant.infrastructure.service.vectordb;

import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.VectorSimilarityUtil;
import com.sankuai.wmbdaiassistant.domain.model.vector.VectorMatchResultModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricConstant;
import com.sankuai.wmbdaiassistant.domain.service.chat.metric.MetricService;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorMatchService;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.custom.CustomModelClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 向量匹配服务实现
 *
 * <AUTHOR>
 * @description 向量相似度匹配服务实现
 * @create 2024/12/24 10:30
 */
@Slf4j
@Service
public class VectorMatchServiceImpl implements VectorMatchService {

    @Resource
    private CustomModelClient customModelClient;

    @Resource
    private MetricService metricService;

    @Override
    public VectorMatchResultModel findMostSimilarPhrase(String input, List<String> phraseList) {
        try {
            // 过滤有效的短语
            List<String> validPhrases = filterAndTrimStrings(phraseList);

            if (CollectionUtils.isEmpty(validPhrases)) {
                log.warn("VectorMatchService: no valid phrases found");
                return null;
            }

            // 准备所有需要编码的文本（输入 + 有效短语列表）
            List<String> allTexts = new ArrayList<>();
            allTexts.add(input.trim());
            allTexts.addAll(validPhrases);

            // 批量获取向量
            List<List<Double>> allVectors = batchEmbedding(allTexts);
            ParamCheckUtil.notEmpty(allVectors, "批量向量转换失败");

            // 第一个向量是输入文本的向量
            List<Double> inputVector = allVectors.get(0);
            ParamCheckUtil.notEmpty(inputVector, "输入字符串向量转换失败");

            // 使用parallelStream并行计算所有相似度分数
            List<Double> similarities = IntStream.range(1, allVectors.size())
                    .parallel()
                    .mapToObj(i -> {
                        List<Double> phraseVector = allVectors.get(i);
                        if (CollectionUtils.isEmpty(phraseVector)) {
                            return -1.0; // 空向量给最低分
                        }
                        try {
                            return VectorSimilarityUtil.cosineSimilarity(inputVector, phraseVector);
                        } catch (Exception e) {
                            log.error("VectorMatchService: error calculating similarity for index {}: {}", i, e.getMessage());
                            return -1.0; // 计算失败给最低分
                        }
                    })
                    .collect(Collectors.toList());

            // 找到最大相似度的索引
            int maxIndex = IntStream.range(0, similarities.size())
                    .boxed()
                    .max(Comparator.comparingDouble(similarities::get))
                    .orElse(-1);

            if (maxIndex == -1 || similarities.get(maxIndex) <= -1.0) {
                log.warn("VectorMatchService: no valid similarity found for input '{}'", input);
                return null;
            }

            // 获取最相似的短语和相似度
            String mostSimilarPhrase = validPhrases.get(maxIndex);
            double maxSimilarity = similarities.get(maxIndex);

            log.info("VectorMatchService: input='{}', mostSimilarPhrase='{}', similarity={}",
                    input, mostSimilarPhrase, maxSimilarity);

            return VectorMatchResultModel.buildResult(mostSimilarPhrase, maxSimilarity);

        } catch (Exception e) {
            log.error("VectorMatchService findMostSimilarPhrase error: input='{}', phraseList size={}, error: {}",
                    input, phraseList.size(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量向量生成
     *
     * @param inputs 输入文本列表
     * @return 向量列表，与输入列表一一对应
     */
    private List<List<Double>> batchEmbedding(List<String> inputs) {
        ParamCheckUtil.notEmpty(inputs, "批量向量生成时，输入列表不能为空");

        return metricService.recordCost(MetricConstant.AI_MODEL_TYPE
                , MetricConstant.AI_MODEL_TYPE_NAME_CHATGLM3_EMBEDDING
                , () -> {
                    // 过滤空字符串
                    List<String> validInputs = filterAndTrimStrings(inputs);

                    if (CollectionUtils.isEmpty(validInputs)) {
                        return Collections.emptyList();
                    }

                    // 使用并行流进行批量处理
                    return validInputs.parallelStream()
                            .map(input -> customModelClient.embedding(input))
                            .collect(Collectors.toList());
                });
    }

    /**
     * 过滤无效字符串并进行trim处理
     *
     * @param inputs 输入字符串列表
     * @return 过滤后的有效字符串列表
     */
    private List<String> filterAndTrimStrings(List<String> inputs) {
        return DefaultUtil.defaultList(inputs).stream()
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
    }
}
