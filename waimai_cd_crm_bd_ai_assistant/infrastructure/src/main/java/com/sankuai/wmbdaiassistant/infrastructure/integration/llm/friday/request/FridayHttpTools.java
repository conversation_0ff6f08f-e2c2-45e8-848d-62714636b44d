package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Friday HTTP API 工具类，包含工具类型和具体函数定义
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 11:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpTools {

    /**
     * 工具类型，通常为 "function"
     */
    private String type;

    /**
     * 工具对应的函数定义
     */
    private FridayHttpToolsFunction function;
}