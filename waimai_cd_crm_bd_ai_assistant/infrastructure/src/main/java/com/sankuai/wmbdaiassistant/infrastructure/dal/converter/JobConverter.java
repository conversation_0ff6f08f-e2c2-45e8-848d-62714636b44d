package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.JobModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job;

/**
 * job
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
public class JobConverter {

    public static JobModel of(Job job) {
        if (job == null) {
            return null;
        }

        JobModel jobModel = new JobModel();
        jobModel.setId(job.getId());
        jobModel.setType(JobTypeEnum.getByCode(job.getType()));
        jobModel.setUid(job.getUid());
        jobModel.setMis(job.getMis());
        jobModel.setCtime(job.getCtime());
        jobModel.setUtime(job.getUtime());

        return jobModel;
    }

    public static Job to(JobModel jobModel) {
        if (jobModel == null) {
            return null;
        }

        Job job = new Job();
        job.setId(jobModel.getId());
        job.setType(jobModel.getType() == null ? null : jobModel.getType().getCode());
        job.setUid(jobModel.getUid());
        job.setMis(jobModel.getMis());
        job.setCtime(jobModel.getCtime());
        job.setUtime(jobModel.getUtime());

        return job;
    }
}
