package com.sankuai.wmbdaiassistant.infrastructure.dal.converter;

import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.enums.JobStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.model.JobTriggerModel;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem;

/**
 * jobItem
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
public class JobItemConverter {

    public static JobItemModel of(JobItem jobItem) {
        if (jobItem == null) {
            return null;
        }

        JobItemModel jobItemModel = new JobItemModel();
        jobItemModel.setId(jobItem.getId());
        jobItemModel.setJobId(jobItem.getJobId());
        jobItemModel.setType(JobTypeEnum.getByCode(jobItem.getType()));
        jobItemModel.setUid(jobItem.getUid());
        jobItemModel.setMis(jobItem.getMis());
        jobItemModel.setEntityType(jobItem.getEntityType());
        jobItemModel.setEntityId(jobItem.getEntityId());
        jobItemModel.setStatus(JobStatusEnum.getByCode(jobItem.getStatus()));
        jobItemModel.setTrigger(JsonUtil.fromJson(jobItem.getTrigger(), JobTriggerModel.class));
        jobItemModel.setCtime(jobItem.getCtime());
        jobItemModel.setUtime(jobItem.getUtime());

        return jobItemModel;
    }

    public static JobItem to(JobItemModel jobItemModel) {
        if (jobItemModel == null) {
            return null;
        }

        JobItem jobItem = new JobItem();
        jobItem.setId(jobItemModel.getId());
        jobItem.setJobId(jobItemModel.getJobId());
        jobItem.setType(jobItemModel.getType() != null ? jobItemModel.getType().getCode() : null);
        jobItem.setUid(jobItemModel.getUid());
        jobItem.setMis(jobItemModel.getMis());
        jobItem.setEntityType(jobItemModel.getEntityType());
        jobItem.setEntityId(jobItemModel.getEntityId());
        jobItem.setStatus(jobItemModel.getStatus() != null ? jobItemModel.getStatus().getCode() : null);
        jobItem.setTrigger(JsonUtil.toJson(jobItemModel.getTrigger()));
        jobItem.setCtime(jobItemModel.getCtime());
        jobItem.setUtime(jobItemModel.getUtime());

        return jobItem;
    }

}
