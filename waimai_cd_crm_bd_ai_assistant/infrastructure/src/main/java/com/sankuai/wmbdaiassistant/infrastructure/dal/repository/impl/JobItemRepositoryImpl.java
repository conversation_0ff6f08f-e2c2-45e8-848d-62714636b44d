package com.sankuai.wmbdaiassistant.infrastructure.dal.repository.impl;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.repository.JobItemRepository;
import com.sankuai.wmbdaiassistant.infrastructure.dal.converter.JobItemConverter;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.JobItem;
import com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobItemExample;
import com.sankuai.wmbdaiassistant.infrastructure.dal.mapper.JobItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * jobItem仓储
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
@Slf4j
@Repository
public class JobItemRepositoryImpl implements JobItemRepository {

    @Resource
    private JobItemMapper jobItemMapper;

    @Override
    public List<JobItemModel> findByJobId(Long jobId) {
        if (jobId == null) {
            return Collections.emptyList();
        }
        JobItemExample jobItemExample = new JobItemExample();
        jobItemExample.createCriteria().andJobIdEqualTo(jobId);
        List<JobItem> jobItems = jobItemMapper.selectByExample(jobItemExample);
        if (CollectionUtils.isEmpty(jobItems)) {
            return Collections.emptyList();
        }

        return jobItems.stream().map(JobItemConverter::of).collect(Collectors.toList());
    }

    @Override
    public List<JobItemModel> findByTypeAndStatus(String type, String status) {
        ParamCheckUtil.notBlank(type, "type不能为空");
        ParamCheckUtil.notBlank(status, "status不能为空");

        JobItemExample jobItemExample = new JobItemExample();
        jobItemExample.createCriteria().andTypeEqualTo(type).andStatusEqualTo(status);
        List<JobItem> jobItems = jobItemMapper.selectByExample(jobItemExample);
        if (CollectionUtils.isEmpty(jobItems)) {
            return Collections.emptyList();
        }

        return jobItems.stream().map(JobItemConverter::of).collect(Collectors.toList());
    }

    @Override
    public Long countByJobIdsAndStatus(List<Long> jobIds, String status) {
        ParamCheckUtil.notEmpty(jobIds, "jobIds不能为空");
        ParamCheckUtil.notBlank(status, "status不能为空");
        JobItemExample example = new JobItemExample();
        example.createCriteria().andJobIdIn(jobIds).andStatusEqualTo(status);
        return jobItemMapper.countByExample(example);
    }

    @Override
    public Long insert(JobItemModel jobItemModel) {
        ParamCheckUtil.notNull(jobItemModel, "jobItemModel不能为空");

        JobItem jobItem = JobItemConverter.to(jobItemModel);

        if (jobItemMapper.insert(jobItem) == 1) {
            return jobItem.getId();
        }
        return null;
    }


    @Override
    public Boolean update(JobItemModel jobItemModel) {
        return jobItemMapper.updateByPrimaryKeySelective(JobItemConverter.to(jobItemModel)) == 1;

    }

    @Override
    public Date findLatestUpdatedDateByJobIds(List<Long> jobIds) {
        ParamCheckUtil.notEmpty(jobIds, "jobIds不能为空");

        JobItemExample example = new JobItemExample();
        example.createCriteria().andJobIdIn(jobIds);
        example.setOrderByClause("utime desc");
        example.limit(1);

        List<JobItem> jobItems = jobItemMapper.selectByExample(example);
        return CollectionUtils.isEmpty(jobItems) ? null : jobItems.get(0).getUtime();
    }
}
