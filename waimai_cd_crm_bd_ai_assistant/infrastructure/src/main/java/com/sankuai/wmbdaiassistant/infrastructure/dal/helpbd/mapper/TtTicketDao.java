package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper;

import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface TtTicketDao {

    /**
     * 插入一条新数据
     * 如果 ticket 存在则会更新字段
     *
     * @param record po
     * @return
     */
    int insertOrUpdateSelective(TtTicketPo record);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return
     */
    TtTicketPo selectByPrimaryKey(Integer id);

    /**
     * 根据 ticketId 查询
     *
     * @param ticketId tt 工单id
     * @return
     */
    TtTicketPo selectByTicketId(Integer ticketId);

    /**
     * 根据主键更新一条数据
     *
     * @param record po
     * @return
     */
    int updateByPrimaryKeySelective(TtTicketPo record);

    /**
     * 根据 ticketId 更新一条数据
     *
     * @param record po
     * @return
     */
    int updateByTicketIdSelective(TtTicketPo record);

    /**
     * 根据 onesId 查询
     *
     * @param onesId onesId
     * @return
     */
    TtTicketPo selectByOnesId(String onesId);

    /**
     * 分页查询未关联 onesId 的工单
     *
     * @param minId 主键游标
     * @param pageSize 分页大小
     * @param startTime 限制最小创建时间
     * @return
     */
    List<TtTicketPo> selectUnOnesRefByPage(@Param("minId") Integer minId,@Param("pageSize") Integer pageSize,@Param("startTime") Date startTime);
}