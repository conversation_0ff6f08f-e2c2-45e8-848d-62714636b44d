package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Friday HTTP API 聊天响应类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 14:40
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpChatResponse {

    /**
     * 聊天完成的唯一标识
     */
    private String id;

    /**
     * 对象类型（如"chat.completion"或"chat.completion.chunk"）
     */
    private String object;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 使用的模型名称
     */
    private String model;

    /**
     * Token使用量统计
     */
    private FridayHttpUsage usage;

    /**
     * 聊天选择结果列表
     */
    private List<FridayHttpChoice> choices;

    /**
     * 流式返回时的内容（仅流式模式有此字段）
     */
    private String content;

    /**
     * 是否为流式返回的最后一条（仅流式模式有此字段）
     */
    @JsonProperty("lastOne")
    private Boolean isLastOne;
}