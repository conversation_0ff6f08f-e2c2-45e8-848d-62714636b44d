package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * Friday HTTP API 消息类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 10:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpMessage {

    /**
     * 用户角色，有 "system", "user", "assistant", "tool"
     */
    @NonNull
    private String role;

    /**
     * 对话内容
     */
    private String content;

    /**
     * 工具调用
     */
    @JsonProperty("tool_calls")
    private List<FridayHttpToolCalls> toolCalls;

    /**
     * 工具调用的ID
     */
    @JsonProperty("tool_call_id")
    private List<FridayHttpToolCalls> toolCallsId;

    /**
     * 任务ID（可选）
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * 推理内容（可选）
     */
    @JsonProperty("reasoning_content")
    private String reasoningContent;

    /**
     * 推理详情（可选）
     */
    @JsonProperty("reasoning_details")
    private Object reasoningDetails;

    public FridayHttpMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }
}