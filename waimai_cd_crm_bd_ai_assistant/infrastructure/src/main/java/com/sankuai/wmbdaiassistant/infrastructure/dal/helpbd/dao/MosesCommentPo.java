package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 *
 *   表名: bd_help_moses_comment
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MosesCommentPo {
    /**
     *   字段: id
     *   说明: 主键id
     */
    private Long id;

    /**
     *   字段: mis_id
     *   说明: 评价人的 misId
     */
    private String misId;

    /**
     *   字段: uid
     *   说明: 评价人的 uid
     */
    private Long uid;

    /**
     *   字段: type
     *   说明: 评论类型：close:直接关闭；submit:提交
     */
    private String type;

    /**
     *   字段: tenant_id
     *   说明: infra 的团队管理的租户ID
     */
    private Long tenantId;

    /**
     *   字段: tenant_name
     *   说明: infra 的团队管理的租户名称
     */
    private String tenantName;

    /**
     *   字段: biz_id
     *   说明: infra 的团队管理的业务ID
     */
    private Long bizId;

    /**
     *   字段: biz_name
     *   说明: infra 的团队管理的业务名字
     */
    private String bizName;

    /**
     *   字段: robot_type
     *   说明: 机器人类型，0: 摩西机器人 1: 智能客服
     */
    private Integer robotType;

    /**
     *   字段: robot_id
     *   说明: 摩西机器人ID/智能客服url
     */
    private String robotId;

    /**
     *   字段: star
     *   说明: 评分，1 ~ 5，分别对应 1 ~ 5 星，5代表5星好评，是最高评价
     */
    private Integer star;

    /**
     *   字段: value
     *   说明: 值
     */
    private Integer value;

    /**
     *   字段: desc
     *   说明: 评分所对应的描述
     */
    private String desc;

    /**
     *   字段: tips
     *   说明: 提示，多个 tip 通过逗号分隔
     */
    private String tips;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;

    /**
     *   字段: comment
     *   说明: 用户输入的评论
     */
    private String comment;
}