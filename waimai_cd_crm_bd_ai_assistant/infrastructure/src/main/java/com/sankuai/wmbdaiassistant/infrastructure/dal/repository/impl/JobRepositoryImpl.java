package com.sankuai.wmbdaiassistant.infrastructure.dal.repository.impl;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.JobModel;
import com.sankuai.wmbdaiassistant.domain.repository.JobRepository;
import com.sankuai.wmbdaiassistant.infrastructure.dal.converter.JobConverter;
import com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject.Job;
import com.sankuai.wmbdaiassistant.infrastructure.dal.example.JobExample;
import com.sankuai.wmbdaiassistant.infrastructure.dal.mapper.JobMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * job仓储
 *
 * <AUTHOR>
 * @date 2025/07/08
 */
@Slf4j
@Repository
public class JobRepositoryImpl implements JobRepository {

    @Resource
    private JobMapper jobMapper;

    @Override
    public List<JobModel> findByMisAndTypeAndDateRange(String mis, String type, Date startDate, Date endDate) {
        ParamCheckUtil.notBlank(mis, "mis不能为空");
        ParamCheckUtil.notBlank(type, "type不能为空");
        ParamCheckUtil.notNull(startDate, "startDate不能为空");
        ParamCheckUtil.notNull(endDate, "endDate不能为空");

        JobExample jobExample = new JobExample();
        jobExample.createCriteria().andMisEqualTo(mis).andTypeEqualTo(type).andCtimeBetween(startDate, endDate);
        jobExample.setOrderByClause("ctime desc");

        List<Job> jobs = jobMapper.selectByExample(jobExample);

        if (CollectionUtils.isEmpty(jobs)) {
            return Collections.emptyList();
        }
        return jobs.stream().map(JobConverter::of).collect(Collectors.toList());    }


    @Override
    public Long insert(JobModel jobModel) {
        ParamCheckUtil.notNull(jobModel, "jobModel不能为空");

        Job job = JobConverter.to(jobModel);

        if (jobMapper.insert(job) == 1) {
            return job.getId();
        }
        return null;
    }
}
