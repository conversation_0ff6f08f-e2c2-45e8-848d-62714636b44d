package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.List;

/**
 * Friday HTTP API 统一请求类
 *
 * <AUTHOR> <qia<PERSON><PERSON><EMAIL>>
 * @date 2025-06-19 10:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpChatRequest {

    /**
     * 模型ID
     */
    private String model;

    /**
     * 生成的聊天消息
     */
    private List<FridayHttpMessage> messages;

    /**
     * true：流式输出（注意：需要确认被调用模型是否支持流式）
     * false：段式输出
     *
     * 默认为false
     */
    private Boolean stream;

    /**
     * 采样值，确定输出的随机性
     *
     * 默认：1
     */
    private Double temperature;

    /**
     * 核采样值
     *
     * 默认：1
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * 聊天完成选项生成个数
     *
     * 默认：1
     */
    private Integer n;

    /**
     * user字段可以传输一个唯一标识的id，用于问题排查。例如traceId、uuid、requestId等。
     */
    private String user;

    /**
     * 最大令牌数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens;

    /**
     * 停止生成更多令牌的最多4个序列
     *
     * 默认：null
     */
    private List<String> stop;

    /**
     * 根据是否出现在文本中来惩罚新标记
     *
     * 默认：0
     */
    @JsonProperty("presence_penalty")
    private Double presencePenalty;

    /**
     * 根据在文本中的现有频率对其进行惩罚
     *
     * 默认：0
     */
    @JsonProperty("frequency_penalty")
    private Double frequencyPenalty;

    /**
     * 函数
     */
    private List<FridayHttpTools> tools;

    /**
     * 控制模型调用的函数
     */
    @JsonProperty("tool_choice")
    private Object toolChoice;

}
