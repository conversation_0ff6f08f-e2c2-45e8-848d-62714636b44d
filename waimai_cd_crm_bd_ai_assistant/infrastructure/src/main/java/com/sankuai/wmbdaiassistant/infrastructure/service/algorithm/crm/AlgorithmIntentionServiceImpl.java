package com.sankuai.wmbdaiassistant.infrastructure.service.algorithm.crm;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.ChatRecordBo;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.enums.ChatContentTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.IntentRecognitionRespData;
import com.sankuai.wmbdaiassistant.domain.service.chat.ChatRecordService;
import com.sankuai.wmbdaiassistant.domain.service.chat.algorithm.crm.AlgorithmIntentionService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.JettyClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlgorithmIntentionServiceImpl implements AlgorithmIntentionService {

    @Resource
    protected ChatRecordService chatRecordService;

    private WebClient webClient;

    @MdpConfig("intention_recognition_service_url:xxxx")
    private String intentionRecognitionServiceUrl;


    @MdpConfig("intention_web_client_config:{\"executorCoreSize\":100,\"executorMaxSize\":1000,\"executorKeepAliveTimeMinutes\":1,\"executorMaxQueueSize\":1000,\"maxConnPerDestination\":200,\"idleTimeOut\":60000,\"connTimeout\":60000,\"maxReqQueuePerDestination\":1000}")
    private void intentionWebClientConfig(HashMap<String, Integer> config) {
        ExecutorService executorService = Rhino
                .newThreadPool("IntentionWebClientThreadPool",
                        DefaultThreadPoolProperties.Setter()
                                .withCoreSize(config.get("executorCoreSize"))
                                .withMaxSize(config.get("executorMaxSize"))
                                .withTraceable(true)
                                .withKeepAliveTimeMinutes(config.get("executorKeepAliveTimeMinutes"))
                                .withMaxQueueSize(config.get("executorMaxQueueSize"))
                ).getExecutor();

        SslContextFactory.Client sslContextFactory = new SslContextFactory.Client();
        HttpClient httpClient = new HttpClient(sslContextFactory);
        httpClient.setExecutor(executorService);
        // 配置连接池
        httpClient.setMaxConnectionsPerDestination(config.get("maxConnPerDestination")); // 每个目标主机的最大连接数
        httpClient.setIdleTimeout(config.get("idleTimeOut")); // 空闲连接超时时间（单位：毫秒）
        httpClient.setConnectTimeout(config.get("connTimeout")); // 连接超时时间（单位：毫秒）
        httpClient.setMaxRequestsQueuedPerDestination(config.get("maxReqQueuePerDestination")); // 每个目标主机的最大排队请求数
        webClient = WebClient.builder().clientConnector(new JettyClientHttpConnector(httpClient)).build();
    }

    @Override
    public boolean recognizeIntentionStream(SessionBo sessionBo, String input, Consumer<IntentRecognitionRespData> outputConsumer, Consumer<Throwable> onError) {

        try {
            // 查询聊天记录
            List<ChatRecordBo> history = chatRecordService.fetchSessionHistory(sessionBo.getSessionId());

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("mis_id", sessionBo.getMis());
            requestBody.put("query", input);
            requestBody.put("history", DefaultUtil.defaultList(history).stream().map(chatRecordBo -> {
                Map<String, String> message = new HashMap<>();
                if (ChatContentTypeEnum.QUESTION.equals(chatRecordBo.getType())) {
                    message.put("role", "user");
                    message.put("content", chatRecordBo.getContent());
                } else if (ChatContentTypeEnum.ANSWER.equals(chatRecordBo.getType())) {
                    message.put("role", "assistant");
                    message.put("content", chatRecordBo.getContent());
                }
                return message;
            }).collect(Collectors.toList()));
            log.info("recognizeIntentionStream request info, session={},mis={},requestBody:{}",sessionBo.getSessionId(), sessionBo.getMis(), JsonUtil.toJson(requestBody));

            // 发送流式请求
            webClient.post()
                    .uri(intentionRecognitionServiceUrl)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToFlux(String.class)
                    .subscribe(
                            response -> {
                                log.info("recognizeIntentionStream response info,session:{},mis={},raw data:{}", sessionBo.getSessionId(), sessionBo.getMis(), response);

                                IntentRecognitionRespData respData = JsonUtil.fromJson(response, IntentRecognitionRespData.class);
                                outputConsumer.accept(respData);
                            },
                            error -> {
                                log.error("session:{},recognizeIntentionStream error for user {}: {}", sessionBo.getSessionId(), sessionBo.getMis(), error.getMessage(), error);
                                onError.accept(error);
                            }
                    );
            return false;
        } catch (Exception e) {
            log.error("session:{},recognizeIntentionStream failed for user {}: {}", sessionBo.getSessionId(), sessionBo.getMis(), e.getMessage(), e);
        }

        return true;
    }
}
