package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Friday HTTP API 函数参数类：描述函数的参数结构
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 11:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpToolsFunctionParameters {

    /**
     * 参数类型，通常为"object"
     */
    private String type = "object";

    /**
     * 参数属性定义，key为属性名，value为属性描述对象
     */
    private Map<String, FridayHttpFunctionProperty> properties = new HashMap<>();

    /**
     * 必填参数名称列表
     */
    private List<String> required;
    
    public void addProperty(FridayHttpFunctionProperty property) {
        this.properties.put(property.getName(), property);
        if (property.getRequired()) {
            if (this.required == null) {
                this.required = new ArrayList<>();
            }
            this.required.add(property.getName());
        }
    }
}