package com.sankuai.wmbdaiassistant.infrastructure.dal.repository.impl;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest.Builder;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.DatasetModel;
import com.sankuai.wmbdaiassistant.domain.model.FragmentCoreModel;
import com.sankuai.wmbdaiassistant.domain.repository.DatasetRepository;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentCoreRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentCoreQuery;
import com.sankuai.wmbdaiassistant.infrastructure.dal.converter.FragmentConverter;
import com.sankuai.wmbdaiassistant.infrastructure.integration.es.PorosJavaApiEsClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Fragment 核心模型仓储实现
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-27 16:52
 */
@Repository
public class FragmentCoreRepositoryImpl implements FragmentCoreRepository {

    private static final int MAX_BATCH_SIZE = 100;

    @Resource
    private PorosJavaApiEsClient porosJavaApiEsClient;

    @Resource
    private DatasetRepository datasetRepository;

    @MdpConfig("dataset.index.name.map:{\n" +
            "  \"直营白领知识库\": \"bailing_fragment_index\",\n" +
            "  \"直营校园知识库\": \"school_fragment_index\",\n" +
            "  \"拼好饭知识库\": \"phf_fragment_index\",\n" +
            "  \"全国KA知识库\": \"ka_fragment_index\",\n" +
            "  \"区域KA知识库\": \"cka_fragment_index\",\n" +
            "  \"远程运营知识库\": \"remote_fragment_index\",\n" +
            "  \"商家通用知识库\": \"poi_common_fragment_index\",\n" +
            "  \"测试知识库\": \"test_fragment_index\",\n" +
            "  \"智能入驻帮手知识库\": \"ruzhu_fragment_index\",\n" +
            "  \"外卖新签电销\": \"wm_new_sign_fragment_index\"\n" +
            "}")
    private HashMap<String, String> datasetIndexNameMap;

    @MdpConfig("fragment.core.query.max.size:10000")
    private Integer queryMaxSize;

    @Override
    public List<FragmentCoreModel> findByQuery(FragmentCoreQuery query) {
        ParamCheckUtil.notNull(query, "查询参数不能为空");
        ParamCheckUtil.notNull(query.getDatasetId(), "知识库ID不能为空");

        Builder searchSourceBuilder = new Builder();
        searchSourceBuilder.index(getIndexName(query.getDatasetId()));

        searchSourceBuilder.query(q -> q.bool(buildQuery(query)));

        if (query.getPageSize() == null) {
            query.setPageSize(queryMaxSize);
        }

        searchSourceBuilder.size(query.getPageSize());
        if (query.getPageNum() != null && query.getPageNum() * query.getPageSize() < queryMaxSize) {
            searchSourceBuilder.from((query.getPageNum() - 1) * query.getPageSize());
        }

        return DefaultUtil.defaultList(porosJavaApiEsClient.query(searchSourceBuilder.build())).stream()
                .map(FragmentConverter::ofCore).collect(Collectors.toList());
    }

    @Override
    public void batchUpsert(Long datasetId, List<FragmentCoreModel> fragmentCoreModelList) {
        if (CollectionUtils.isEmpty(fragmentCoreModelList)) {
            return;
        }
        String indexName = getIndexName(datasetId);
        List<List<FragmentCoreModel>> partitions = Lists.partition(fragmentCoreModelList, MAX_BATCH_SIZE);
        for (List<FragmentCoreModel> partition : partitions) {
            Map<String, Map<String, Object>> data = partition.stream()
                    .collect(Collectors.toMap(
                            FragmentCoreModel::getId,
                            FragmentCoreModel::toMap,
                            (k1, k2) -> k1
                    ));
            porosJavaApiEsClient.batchInsertOrUpdate(indexName, data);
        }
    }

    @Override
    public void batchDelete(Long datasetId, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        String indexName = getIndexName(datasetId);
        porosJavaApiEsClient.batchDelete(indexName, idList);
    }

    private BoolQuery buildQuery(FragmentCoreQuery query) {
        BoolQuery.Builder queryBuilder = new BoolQuery.Builder();
        if (query.getWikiId() != null) {
            queryBuilder.must(q -> q.term(t -> t.field(FragmentCoreModel.WIKI_ID).value(query.getWikiId())));
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryBuilder.must(q -> q.match(t -> t.field(FragmentCoreModel.MERGE_CONTENT).query(query.getKeyword())));
        }
        return queryBuilder.build();
    }

    private String getIndexName(Long datasetId) {
        DatasetModel datasetModel = datasetRepository.findById(datasetId);
        ParamCheckUtil.notNull(datasetModel, "知识库不存在");

        return datasetIndexNameMap.get(datasetModel.getName());
    }
}
