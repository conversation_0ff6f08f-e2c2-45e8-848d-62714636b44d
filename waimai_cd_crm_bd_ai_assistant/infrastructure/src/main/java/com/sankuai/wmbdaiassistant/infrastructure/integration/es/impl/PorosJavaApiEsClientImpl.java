package com.sankuai.wmbdaiassistant.infrastructure.integration.es.impl;

import co.elastic.clients.elasticsearch._types.Refresh;
import co.elastic.clients.elasticsearch.core.*;

import co.elastic.clients.elasticsearch.core.search.Hit;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.poros.client.PorosApiClient;
import com.sankuai.meituan.poros.client.PorosApiClientBuilder;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.infrastructure.integration.es.PorosJavaApiEsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PorosJavaApiEsClientImpl implements PorosJavaApiEsClient {

    /**
     * es 中的主键ID
     */
    private String ID = "_id";

    @Value("${es8.clusterName}")
    private String clusterName;

    @Value("${app.name}")
    private String appName;

    @MdpConfig("es.batch.write.timeout:10s")
    private String batchWriteTimeout;

    /**
     * es索引名称映射，这里是指es7的索引对应到es8中索引名称，如果没有对应关系，则es7的索引名称和es8的索引名称一致
     */
    @MdpConfig("es.write.index.name.map:{}")
    private HashMap<String, String> indexNameMap;

    private PorosApiClient porosApiClient;

    @PostConstruct
    public void init() {
        porosApiClient = PorosApiClientBuilder.builder()
                .clusterName(clusterName)
                .appKey(appName)
                .build();
    }

    /**
     * 批量插入或者更新
     * @param indexName 索引名字
     * @param data      数据
     * @return
     */
    @Override
    public boolean batchInsertOrUpdate(String indexName, Map<String, Map<String, Object>> data) {

        String realIndexName = DefaultUtil.defaultValue(DefaultUtil.defaultMap(indexNameMap).get(indexName), indexName);
        
        BulkRequest.Builder br = new BulkRequest.Builder();
        br.refresh(Refresh.WaitFor);
        br.timeout(t->t.time(batchWriteTimeout));

        for (Map.Entry<String, Map<String, Object>> entry : data.entrySet()) {
            String id = entry.getKey();
            Map<String, Object> dataMap = entry.getValue();

            br.operations(operations -> operations
                    .update(idx -> idx
                            .index(realIndexName)
                            .id(id)
                            .action(a->a
                                    .doc(dataMap)
                                    .docAsUpsert(true))));
        }

        try {
            BulkResponse response = porosApiClient.bulk(br.build());
            return response!=null && !response.errors();
        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl batchInsertOrUpdate exception, msg = {}", e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean batchDelete(String indexName, List<String> idList) {

        String realIndexName = DefaultUtil.defaultValue(DefaultUtil.defaultMap(indexNameMap).get(indexName), indexName);

        BulkRequest.Builder br = new BulkRequest.Builder();
        for (String id : idList) {
            br.operations(operations -> operations
                    .delete(d -> d
                            .index(realIndexName)
                            .id(id)));
        }

        try {
            BulkResponse response = porosApiClient.bulk(br.build());
            return response!=null && !response.errors() ;
        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl batchDelete exception, msg = {}", e.getMessage(), e);
        }
        return false;
    }

    @Override
    public List<Map<String, String>> query(SearchRequest request) {
        try {
            SearchResponse response = porosApiClient.search(request, Map.class);

            if(response == null || response.hits() == null || response.hits().hits() == null){
                return Collections.emptyList();
            }

            List<Hit<Map<String, Object>>> hitList = response.hits().hits();

            return hitList.stream().map(hit-> {
                Map<String, String> stringMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : hit.source().entrySet()) {
                    stringMap.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
                stringMap.put("_score", String.valueOf(hit.score()));
                return stringMap;
            }).collect(Collectors.toList());


        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl query exception, msg = {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public SearchResponse search(SearchRequest request) {
        try {
            return porosApiClient.search(request, Map.class);
        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl search exception, msg = {}", e.getMessage(), e);
        }

        return null;
    }

    @Override
    public void scroll(SearchRequest request,Consumer<List<Map<String, String>>> consumer) {

        String scrollId = "";

        try {
            SearchResponse response = porosApiClient.search(request, Map.class);

            if(response == null || response.hits() == null || CollectionUtils.isEmpty(response.hits().hits())){
                return;
            }

            scrollId = response.scrollId();

            log.info("PorosJavaApiEsClientImpl scroll first query response = {}", response);
            List<Hit<Map<String, Object>>> hitList = response.hits().hits();

            // 消费第一次的数据
            consumer.accept(parseHits(hitList));


            ScrollRequest.Builder scrollRequestBuilder = new ScrollRequest.Builder();
            scrollRequestBuilder.scrollId(scrollId);
            scrollRequestBuilder.scroll(request.scroll());
            ScrollResponse scrollResponse = porosApiClient.scroll(scrollRequestBuilder.build(), Map.class);

            while (scrollResponse != null
                    && scrollResponse.hits() != null
                    && scrollResponse.hits().hits() != null
                    && CollectionUtils.isNotEmpty(scrollResponse.hits().hits())){

                log.info("PorosJavaApiEsClientImpl scroll query response = {}", scrollResponse);
                hitList = scrollResponse.hits().hits();

                consumer.accept(parseHits(hitList));

                ScrollRequest.Builder srb = new ScrollRequest.Builder();
                srb.scrollId(scrollResponse.scrollId());
                srb.scroll(request.scroll());
                scrollResponse = porosApiClient.scroll(srb.build(), Map.class);
            }

        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl search exception, msg = {}", e.getMessage(), e);
        } finally {
            if (StringUtils.isNotBlank(scrollId)) {
                // 清除scroll上下文
                try {
                    porosApiClient.clearScroll(new ClearScrollRequest.Builder().scrollId(scrollId).build());
                } catch (Exception e) {
                    log.error("PorosJavaApiEsClientImpl clearScroll exception, msg = {}", e.getMessage(), e);
                }

            }
        }
    }

    @Override
    public long count(CountRequest request) {
        try {
            CountResponse response = porosApiClient.count(request);
            return response.count();
        } catch (IOException e) {
            log.error("PorosJavaApiEsClientImpl count error, msg = {}", e.getMessage(), e);
        }

        return 0L;
    }

    /**
     * 解析命中结果
     *
     * @param hitList
     * @return
     */
    private List<Map<String, String>> parseHits(List<Hit<Map<String, Object>>> hitList) {
        return hitList.stream().map(hit -> {
            Map<String, String> stringMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : hit.source().entrySet()) {
                stringMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
            stringMap.put(ID, hit.id());
            return stringMap;
        }).collect(Collectors.toList());
    }
}
