package com.sankuai.wmbdaiassistant.infrastructure.integration.agent.poidiagnose.dto;

import lombok.Data;

/**
 * 诊断数据是否就绪 与 是否支持诊断
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
public class PoiDiagnosticReadyResponseDto {

    /**
     * 数据是否就绪
     */
    Boolean dataReady;

    /**
     * 系统诊断错误原因
     */
    String systemDiagnoseErrorReason;

    /**
     * 不支持诊断原因 为null表示支持诊断，不为null表示不支持诊断
     */
    String unsupportedDiagnoseReason;

    public boolean isDataReady() {
        return dataReady != null && dataReady && !isUnSupportDiagnose() && !isSystemDiagnoseError();
    }

    public boolean isDataAbnormal() {
        return isUnSupportDiagnose() || isSystemDiagnoseError();
    }

    public boolean isUnSupportDiagnose() {
        return this.unsupportedDiagnoseReason != null;
    }

    public boolean isSystemDiagnoseError() {
        return this.systemDiagnoseErrorReason != null;
    }


}
