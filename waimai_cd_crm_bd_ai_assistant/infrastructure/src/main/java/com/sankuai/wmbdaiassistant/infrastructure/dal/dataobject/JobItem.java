package com.sankuai.wmbdaiassistant.infrastructure.dal.dataobject;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: bd_ai_assistant_job_item
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobItem {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: job_id
     *   说明: 任务id
     */
    private Long jobId;

    /**
     *   字段: type
     *   说明: 任务类型
     */
    private String type;

    /**
     *   字段: uid
     *   说明: 用户id
     */
    private Integer uid;

    /**
     *   字段: mis
     *   说明: 用户mis
     */
    private String mis;

    /**
     *   字段: entity_type
     *   说明: 实体类型
     */
    private String entityType;

    /**
     *   字段: entity_id
     *   说明: 实体id
     */
    private String entityId;

    /**
     *   字段: status
     *   说明: 任务状态
     */
    private String status;

    /**
     *   字段: trigger
     *   说明: 触发内容
     */
    private String trigger;

    /**
     *   字段: ctime
     *   说明: 创建时间
     */
    private Date ctime;

    /**
     *   字段: utime
     *   说明: 更新时间
     */
    private Date utime;
}