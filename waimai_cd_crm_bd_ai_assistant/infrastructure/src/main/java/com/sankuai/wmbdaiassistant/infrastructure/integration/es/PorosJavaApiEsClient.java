package com.sankuai.wmbdaiassistant.infrastructure.integration.es;

import co.elastic.clients.elasticsearch.core.CountRequest;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * ES 服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-13 21:49
 */
public interface PorosJavaApiEsClient {
    /**
     * 插入或者更新
     *
     * @param indexName 索引名字
     * @param data 数据
     * @return
     */
    boolean batchInsertOrUpdate(String indexName, Map<String, Map<String, Object>> data);

    /**
     * 批量删除数据
     *
     * @param indexName 索引名字
     * @param idList 主键ID列表
     * @return
     */
    boolean batchDelete(String indexName, List<String> idList);

    /**
     * 搜索
     * 
     * @param request
     */
    List<Map<String, String>> query(SearchRequest request);

    /**
     * 根据条件搜索
     * @param request
     * @return
     */
    SearchResponse search(SearchRequest request);

    /**
     * 游标查询
     */
    void scroll(SearchRequest request,Consumer<List<Map<String, String>>> consumer);

    /**
     * 计数
     * 
     * @param request
     * @return
     */
    long count(CountRequest request);
}
