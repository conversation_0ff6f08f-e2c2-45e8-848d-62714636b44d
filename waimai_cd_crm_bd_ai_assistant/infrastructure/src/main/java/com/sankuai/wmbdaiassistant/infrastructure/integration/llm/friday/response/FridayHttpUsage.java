package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Friday HTTP API Token使用量统计类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 14:45
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpUsage {

    /**
     * 完成部分使用的Token数量（输出Token）
     */
    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    /**
     * 提示部分使用的Token数量（输入Token）
     */
    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    /**
     * 总Token数量（输入+输出）
     */
    @JsonProperty("total_tokens")
    private Integer totalTokens;

    /**
     * 缓存写入的Token数量
     */
    @JsonProperty("cache_write_tokens")
    private Integer cacheWriteTokens;

    /**
     * 缓存读取的Token数量
     */
    @JsonProperty("cache_read_tokens")
    private Integer cacheReadTokens;
}