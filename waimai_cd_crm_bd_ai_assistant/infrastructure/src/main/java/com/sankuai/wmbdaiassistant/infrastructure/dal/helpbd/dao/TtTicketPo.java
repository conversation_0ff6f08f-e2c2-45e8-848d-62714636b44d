package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 *
 *   表名: tt_ticket
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TtTicketPo {
    /**
     *   字段: id
     *   说明: 主键id
     */
    private Integer id;

    /**
     *   字段: ticket_id
     *   说明: TT的 id，唯一标识
     */
    private Integer ticketId;

    /**
     *   字段: rg_id
     *   说明: TT的 rg 标示 （Resolver Group）服务组单位
     */
    private Integer rgId;

    /**
     *   字段: name
     *   说明: TT的工单的名称
     */
    private String name;

    /**
     *   字段: creator_mis
     *   说明: TT工单创建人
     */
    private String creatorMis;

    /**
     *   字段: state
     *   说明: TT工单状态。未处理,处理中,挂起中,暂停中,重新打开,已解决,已关闭
     */
    private String state;

    /**
     *   字段: ones_id
     *   说明: ones 的 id
     */
    private String onesId;

    /**
     *   字段: onesState
     *   说明: ones 的状态
     */
    private String onesState;

    /**
     *   字段: create_time
     *   说明: TT工单创建时间
     */
    private Date createTime;

    /**
     *   字段: modify_time
     *   说明: TT工单更新时间
     */
    private Date modifyTime;

    /**
     *   字段: version
     *   说明: 版本号
     */
    private Integer version;

    /**
     *   字段: labels
     *   说明: 标签信息
     */
    private String labels;
}