package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request.FridayHttpMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Friday HTTP API 聊天选择结果类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 14:50
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpChoice {

    /**
     * 选择结果的索引
     */
    private Integer index;

    /**
     * 消息内容，支持message和delta两种字段名
     * 普通模式下为message，流式模式下为delta
     */
    @JsonAlias("delta")
    private FridayHttpMessage message;

    /**
     * 完成原因（如"stop"、"tool_calls"、"length"等）
     */
    @JsonProperty("finish_reason")
    private String finishReason;

    /**
     * 日志概率信息（可选）
     */
    private Object logprobs;
}