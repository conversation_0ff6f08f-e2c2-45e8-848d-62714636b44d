package com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Set;

/**
 * Friday HTTP API 函数属性类：描述函数参数的单个属性信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-19 11:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayHttpFunctionProperty {
    /**
     * 属性名称（仅用于内部逻辑，序列化时忽略）
     */
    @NonNull
    @JsonIgnore
    private String name;

    /**
     * 属性名称
     */
    @NonNull
    private String type;

    /**
     * 是否为必填属性（仅用于内部逻辑，序列化时忽略）
     */
    @JsonIgnore
    private Boolean required = false;

    /**
     * 属性描述信息
     */
    private String description;

    /**
     * 属性可选值枚举（序列化为 "enum" 字段）
     */
    @JsonProperty("enum")
    private Set<String> enumValues;
}