package com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.EntryTrackingPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EntryTrackingMapper extends MybatisBaseMapper<EntryTrackingPo, EntryTrackingPoExample, Long> {
    int batchInsert(@Param("list") List<EntryTrackingPo> list);
}