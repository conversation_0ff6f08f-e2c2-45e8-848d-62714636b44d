package com.sankuai.wmbdaiassistant.server.application.service.dsl;

import java.util.List;

import com.sankuai.wmbdaiassistant.domain.model.vector.VectorMatchResultModel;

/**
 * 向量相似度DSL服务
 *
 * <AUTHOR>
 * @description 向量相似度匹配服务（供DSL调用）
 * @create 2024/12/24 10:00
 */
public interface VectorSimilarityDslService {

    /**
     * 根据输入字符串和短语列表，找到向量相似度最高的短语
     * 最大匹配数组长度<默认30个，可以在vector.dsl.max.matach.size这个Lion配置中调整
     *
     * @param input      输入字符串
     * @param phraseList 短语列表
     * @return 匹配结果模型，包含匹配分数和最终选择的字符串；如果没有找到匹配则返回空结果
     */
    VectorMatchResultModel findMostSimilarPhrase(String input, List<String> phraseList);
}
