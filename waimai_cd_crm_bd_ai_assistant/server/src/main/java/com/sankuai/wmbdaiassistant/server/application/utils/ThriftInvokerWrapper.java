package com.sankuai.wmbdaiassistant.server.application.utils;

import com.meituan.mtrace.Tracer;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftBaseDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftCommonDto;
import com.sankuai.wmbdaiassistant.server.application.enums.ResultCodeEnum;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;

/**
 * Thrift调用的包装工具类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 19:28
 */
@Slf4j
public class ThriftInvokerWrapper {

    public static <D> ThriftCommonDto<D> handle(Supplier<D> function) {
        ThriftCommonDto<D> result = new ThriftCommonDto<>();
        try {
            D data = function.get();
            result.setCode(ResultCodeEnum.SUCCESS.getCode());
            result.setMsg(ResultCodeEnum.SUCCESS.getMsg());
            result.setData(data);
            return result;
        }
        catch (IllegalArgumentException e) {
            log.error("调用function: {}, 参数异常", function.getClass().getName(), e);
            result.setCode(ResultCodeEnum.ARGUMENT_ERROR.getCode());
            result.setMsg(e.getMessage());
            return result;
        }
        catch (Exception e) {
            log.error("调用function: {}失败", function.getClass().getName(), e);
            result.setCode(ResultCodeEnum.SYSTEM_ERROR.getCode());
            result.setMsg(ResultCodeEnum.SYSTEM_ERROR.getMsg() + ", traceId:" + Tracer.getServerTracer().getTraceId());
            return result;
        }
    }

    public static ThriftBaseDto handleWithoutResult(Runnable runnable) {
        ThriftBaseDto result = new ThriftBaseDto();
        try {
            runnable.run();
            result.setCode(ResultCodeEnum.SUCCESS.getCode());
            result.setMsg(ResultCodeEnum.SUCCESS.getMsg());
            return result;
        }
        catch (IllegalArgumentException e) {
            log.error("调用function: {}, 参数异常", runnable.getClass().getName(), e);
            result.setCode(ResultCodeEnum.ARGUMENT_ERROR.getCode());
            result.setMsg(e.getMessage());
            return result;
        }
        catch (Exception e) {
            log.error("调用function: {}失败", runnable.getClass().getName(), e);
            result.setCode(ResultCodeEnum.SYSTEM_ERROR.getCode());
            result.setMsg(ResultCodeEnum.SYSTEM_ERROR.getMsg() + ", traceId:" + Tracer.getServerTracer().getTraceId());
            return result;
        }
    }

    public static void handleWithVoid(Runnable runnable) {
        try {
            runnable.run();
        }
        catch (IllegalArgumentException e) {
            log.error("调用function: {}, 参数异常", runnable.getClass().getName(), e);
        }
        catch (Exception e) {
            log.error("调用function: {}失败", runnable.getClass().getName(), e);
        }
    }
}
