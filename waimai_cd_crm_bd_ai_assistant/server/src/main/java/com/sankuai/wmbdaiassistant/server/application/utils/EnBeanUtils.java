package com.sankuai.wmbdaiassistant.server.application.utils;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

/**
 * 增强的 BeanUtils
 *
 * <AUTHOR>
 */
public class EnBeanUtils {

    public static <S, T> T copy(S src, Supplier<T> constructor) {
        return copy(src, constructor, null);
    }

    public static <S, T> T copy(S src, Supplier<T> constructor, BiConsumer<S, T> diff) {
        if (Objects.isNull(src)) {
            return null;
        }

        T targetObject = constructor.get();
        copy(src, targetObject);

        if (Objects.nonNull(diff)) {
            diff.accept(src, targetObject);
        }
        return targetObject;
    }

    public static void copy(Object src, Object target) {
        BeanUtils.copyProperties(src, target);
    }

    public static <S, T> List<T> copyList(List<S> src, Supplier<T> constructor) {
        return copyList(src, constructor, null);
    }

    public static <S, T> List<T> copyList(List<S> src, Supplier<T> constructor, BiConsumer<S, T> diff) {
        if (CollectionUtils.isEmpty(src)) {
            return Lists.newArrayList();
        }

        return src.stream()
                .map(s -> copy(s, constructor, diff))
                .collect(Collectors.toList());
    }
}