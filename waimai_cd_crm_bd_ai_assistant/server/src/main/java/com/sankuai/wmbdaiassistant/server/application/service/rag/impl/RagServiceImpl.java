package com.sankuai.wmbdaiassistant.server.application.service.rag.impl;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.request.FaqAddRequestDto;
import com.sankuai.wmbdaiassistant.api.request.FaqModifyRequestDto;
import com.sankuai.wmbdaiassistant.api.response.StandardPhraseDetailResponseDto;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.PhraseStateEnum;
import com.sankuai.wmbdaiassistant.domain.enums.TriggerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.PhraseModel;
import com.sankuai.wmbdaiassistant.domain.model.SceneModel;
import com.sankuai.wmbdaiassistant.domain.repository.DomainRepository;
import com.sankuai.wmbdaiassistant.domain.repository.PhraseRepository;
import com.sankuai.wmbdaiassistant.domain.repository.SceneRepository;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorDatabaseService;
import com.sankuai.wmbdaiassistant.domain.transaction.TransactionHelper;
import com.sankuai.wmbdaiassistant.server.application.service.domain.DomainService;
import com.sankuai.wmbdaiassistant.server.application.service.rag.RagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * RAG
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-03 15:32
 */
@Slf4j
@Service
public class RagServiceImpl implements RagService {

    @Resource
    private PhraseRepository phraseRepository;

    @Resource
    private DomainRepository domainRepository;

    @Resource
    private SceneRepository sceneRepository;

    @Resource
    private TransactionHelper transactionHelper;

    @Resource
    private VectorDatabaseService vectorDatabaseService;

    @Resource
    private DomainService domainService;


    @Override
    public Long add(FaqAddRequestDto requestDto, User user) {
        ParamCheckUtil.notNull(requestDto, "RAG 增加时，请求为空");

        return transactionHelper.doInTransaction(() -> {
            boolean repeat = phraseRepository.checkPhraseRepeat(requestDto.getQuestion(), requestDto.getDomainId());
            ParamCheckUtil.isTrue(!repeat, "存在同名的标准问或者扩展问");

            Long phraseId = this.addDisableRagIntoDB(requestDto, user);
            ParamCheckUtil.isTrue(this.enable(phraseId, user), "RAG增加失败，未能启用");
            return phraseId;
        });
    }

    @Override
    public boolean modify(FaqModifyRequestDto requestDto, User user) {
        ParamCheckUtil.notNull(requestDto.getPhraseId(), "RAG修改时，phraseId 为空");
        ParamCheckUtil.notBlank(requestDto.getQuestion(), "RAG修改时，问题不能为空");

        return transactionHelper.doInTransaction(() -> {
            PhraseModel phraseModel = phraseRepository.findById(requestDto.getPhraseId());
            ParamCheckUtil.notNull(phraseModel, "RAG修改时，标准问法不存在");

            ParamCheckUtil.isTrue(domainRepository.isValid(requestDto.getDomainId()), "RAG修改时，域不存在");


            boolean questionModify = !StringUtils.equals(requestDto.getQuestion(), phraseModel.getStandardizedPhrase());
            boolean domainModify = !Objects.equals(requestDto.getDomainId(), phraseModel.getDomainId());
            if (questionModify || domainModify) {
                boolean repeat = phraseRepository.checkPhraseRepeat(requestDto.getQuestion(), requestDto.getDomainId());
                ParamCheckUtil.isTrue(!repeat, "存在同名的标准问或者扩展问");
                // 更新 phrase
                List<PhraseModel> phraseModelList = phraseRepository
                        .findByStandardizedPhrase(phraseModel.getStandardizedPhrase(), phraseModel.getDomainId());
                ParamCheckUtil.notNull(phraseModel, "RAG修改，标准问法不存在");

                phraseModelList.forEach(model -> {
                    if (model.isStandardPhrase()) {
                        model = phraseModel;
                        model.setPhrase(requestDto.getQuestion());
                    }
                    model.setDomainId(requestDto.getDomainId());
                    model.setStandardizedPhrase(requestDto.getQuestion());
                    model.setModifierMis(user.getLogin());
                    model.setModifierName(user.getName());
                    model.setModifyTime(new Date());
                    phraseRepository.update(model);
                });
                // 重新生成标准问的向量
                return vectorDatabaseService.modifyPhrase(phraseModel);
            } else {
                phraseModel.setModifyTime(new Date());
                phraseModel.setModifierMis(user.getLogin());
                phraseModel.setModifierName(user.getName());
                phraseRepository.update(phraseModel);
            }

            return true;
        });
    }

    @Override
    public StandardPhraseDetailResponseDto detail(Long phraseId, User user) {
        PhraseModel phraseModel = phraseRepository.findById(phraseId);

        StandardPhraseDetailResponseDto responseDto = new StandardPhraseDetailResponseDto();

        responseDto.setId(null);
        responseDto.setDomainId(phraseModel.getDomainId());
        responseDto.setType(TriggerTypeEnum.RAG.getCode());
        responseDto.setQuestion(phraseModel.getStandardizedPhrase());
        responseDto.setAnswer(null);
        responseDto.setTtUrl(null);
        responseDto.setPhraseId(phraseModel.getId());

        return responseDto;
    }

    @Override
    public boolean delete(Long phraseId, User user) {
        ParamCheckUtil.notNull(phraseId, "RAG删除时，phraseId不能为空");

        PhraseModel phraseModel = phraseRepository.findById(phraseId);
        ParamCheckUtil.notNull(phraseModel, "RAG删除时，phrase不存在");

        if (PhraseStateEnum.DELETE.equals(phraseModel.getState())) {
            return true;
        }

        List<SceneModel> sceneModelList = sceneRepository
                .findByPhraseIds(Collections.singletonList(phraseModel.getId()));
        ParamCheckUtil.isTrue(CollectionUtils.isEmpty(sceneModelList),
                "FAQ删除时，phrase被场景引用,sceneModelList:" + sceneModelList);

        return transactionHelper.doInTransaction(() -> {

            List<PhraseModel> phraseModelList = phraseRepository.findByStandardizedPhrase(phraseModel.getStandardizedPhrase(), phraseModel.getDomainId());
            ParamCheckUtil.isTrue(vectorDatabaseService.deleteByPhraseList(phraseModelList), "RAG删除时，向量删除失败");

            phraseModelList.forEach(model -> {
                model.setState(PhraseStateEnum.DELETE);
                model.setModifierName(user.getName());
                model.setModifierMis(user.getLogin());
                model.setModifyTime(new Date());
                phraseRepository.update(model);
            });
            return true;
        });
    }

    @Override
    public boolean enable(Long phraseId, User user) {
        ParamCheckUtil.notNull(phraseId, "RAG启用时，phraseId不能为空");

        PhraseModel phraseModel = phraseRepository.findById(phraseId);
        ParamCheckUtil.notNull(phraseModel, "FAQ启用时,phraseModel不存在,phraseId:" + phraseId);

        if (PhraseStateEnum.ENABLE.equals(phraseModel.getState())) {
            return true;
        }

        return transactionHelper.doInTransaction(() -> {
            List<PhraseModel> phraseModelList = phraseRepository.findByStandardizedPhrase(
                    phraseModel.getStandardizedPhrase(), phraseModel.getDomainId(),
                    PhraseStateEnum.DISABLE);
            phraseModelList.forEach(model -> {
                model.setState(PhraseStateEnum.ENABLE);
                model.setModifierMis(user.getLogin());
                model.setModifierName(user.getName());
                model.setModifyTime(new Date());
                phraseRepository.update(model);
            });

            phraseModelList.forEach(model -> ParamCheckUtil.isTrue(vectorDatabaseService.insertPhrase(model)));
            return true;
        });

    }

    private Long addDisableRagIntoDB(FaqAddRequestDto requestDto, User user) {
        ParamCheckUtil.notBlank(requestDto.getQuestion(), "RAG增加时，问题不能为空");
        ParamCheckUtil.notNull(user, "RAG 增加时，用户为空");
        ParamCheckUtil.isTrue(domainRepository.isValid(requestDto.getDomainId()), "RAG增加时，域不存在");

        return transactionHelper.doInTransaction(() -> {

            PhraseModel phraseModel = new PhraseModel();
            phraseModel.setDomainId(requestDto.getDomainId());
            phraseModel.setPhrase(requestDto.getQuestion());
            phraseModel.setStandardizedPhrase(requestDto.getQuestion());
            phraseModel.setTriggerId(null);
            phraseModel.setTriggerType(TriggerTypeEnum.RAG);
            phraseModel.setModifierMis(user.getLogin());
            phraseModel.setModifierName(user.getName());
            phraseModel.setState(PhraseStateEnum.DISABLE);
            Long phraseId = phraseRepository.insert(phraseModel);
            ParamCheckUtil.notNull(phraseId, "RAG 增加时，phrase 插入失败");
            phraseModel.setId(phraseId);

            return phraseId;
        });
    }
}
