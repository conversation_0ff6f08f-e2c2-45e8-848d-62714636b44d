package com.sankuai.wmbdaiassistant.server.application.bo;

import com.meituan.mdp.boot.starter.bean.copy.annotation.MdpBeanCopy;
import com.sankuai.wmbdaiassistant.api.response.HotQuestionDto;
import com.sankuai.wmbdaiassistant.api.response.hotquestion.HotQuestionConfigItemDto;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigItem;

@MdpBeanCopy
public interface HotQuestionConfigBeanCopy {

    HotQuestionConfigItem configDtoToBo(HotQuestionConfigItemDto source);

    HotQuestionDto boToDto(HotQuestionBo hotQuestionBo);

}
