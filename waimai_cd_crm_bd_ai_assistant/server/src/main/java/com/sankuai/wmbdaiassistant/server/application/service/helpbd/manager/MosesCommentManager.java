package com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager;

import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.dto.UserDto;
import java.util.List;

/**
 * 摩西服务评论
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-01 14:00
 */
public interface MosesCommentManager {

    /**
     * 评论
     *
     * @param mosesCommentBo
     */
    void comment(MosesCommentBo mosesCommentBo);

    /**
     * 获取用户当天的所有评论
     *
     * @param userDto
     * @return
     */
    List<MosesCommentBo> getUserTodayComments(UserDto userDto);

}
