package com.sankuai.wmbdaiassistant.server.application.service.helpbd.impl;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.wmbdaiassistant.api.response.helpbd.PersonTtInfoDto;
import com.sankuai.wmbdaiassistant.server.application.dto.PersonTtCountInfo;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.TtWrapper;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.TtHelpbdService;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><EMAIL>>
 * @date 2025/6/19 16:20
 */
@Slf4j
@Service
public class TtHelpbdServiceImpl implements TtHelpbdService {
    private static final String CAT_TYPE_QUERY_TT = "cat_type_query_tt";
    private static final String CAT_NAME_QUERY_TT = "cat_name_query_tt";

    private final TtWrapper ttWrapper;

    public TtHelpbdServiceImpl(TtWrapper ttWrapper) {
        this.ttWrapper = ttWrapper;
    }

    @Override
    public @Nullable
    PersonTtInfoDto getPersonalInfo(int uid, String misId) {

        PersonTtCountInfo userTtCountInfo = ttWrapper.getPersonalInfo(misId);
        if (Objects.isNull(userTtCountInfo)) {
            log.warn("tt info result is null, user:{}", misId);
            return null;
        }

        PersonTtInfoDto dto = new PersonTtInfoDto();
        dto.setUnresolved(userTtCountInfo.getUnresolved());
        dto.setReporter(userTtCountInfo.getReporter());
        dto.setTtSchemeParams(HelpBDConfig.TT_SCHEME_PARAMS);
        dto.setUid(uid);
        dto.setMisId(misId);
        return dto;
    }

    @Override
    public JsonNode queryTicket(Integer ticketId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        JsonNode ttDetail = ttWrapper.queryTicket(ticketId);
        stopWatch.stop();
        Cat.newCompletedTransactionWithDuration(CAT_TYPE_QUERY_TT, CAT_NAME_QUERY_TT, stopWatch.getTime());
        log.info("TtServiceImpl queryTicket ticketId = {}, response = {}", ticketId, ttDetail);
        return ttDetail;
    }
}
