package com.sankuai.wmbdaiassistant.server.application.service.dsl.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.domain.bo.DxUserBo;
import com.sankuai.wmbdaiassistant.infrastructure.integration.dx.DxClient;
import com.sankuai.wmbdaiassistant.server.application.bo.HistoryGroupMsgsRequest;
import com.sankuai.wmbdaiassistant.server.application.service.dsl.DxMessageDslService;
import com.sankuai.xm.openplatform.api.entity.GroupMsg;
import com.sankuai.xm.openplatform.api.entity.HistoryGroupMsgReq;
import com.sankuai.xm.openplatform.api.entity.HistoryGroupMsgResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service("DxMessageDslService")
@Slf4j
public class DxMessageDslServiceImpl implements DxMessageDslService {

    private static final Integer HISTORY_MESSAGE_MAX_PAGE_SIZE = 100;
    private static final Integer MAX_LOOP_COUNT = 100;
    //大象消息文本类型
    private static final int DX_TYPE_TEXT = 1;
    //大象消息引用类型
    private static final int DX_TYPE_QUOTE = 20;
    //大象消息base64编码类型
    private static final int DX_TYPE_BASE64 = 17;



    @Resource
    private DxClient dxClient;

    @Override
    public Map<String, String> getHistoryGroupMsgs(HistoryGroupMsgsRequest request) {
        if (Objects.isNull(request) || MapUtils.isEmpty(request.getSearchKeyMap()) || Objects.isNull(request.getGid())
                || Objects.isNull(request.getStartTime()) || Objects.isNull(request.getEndTime())) {
            return new HashMap<>();
        }
        List<Integer> msgTypeList = request.getMessageTypeList();
        Map<String, Set<String>> searchKeyMap = request.getSearchKeyMap();

        //分页查询&过滤大象历史消息
        Map<String, List<GroupMsg>> searchKeyMatchMsgsMap = new HashMap<>();
        long pageToken = 0;
        int loopCount = 0;
        while (loopCount++ < MAX_LOOP_COUNT) {
            HistoryGroupMsgReq historyGroupMsgReq = new HistoryGroupMsgReq();
            historyGroupMsgReq.setPageToken(pageToken);
            historyGroupMsgReq.setGid(request.getGid());
            historyGroupMsgReq.setStartTime(request.getStartTime());
            historyGroupMsgReq.setEndTime(request.getEndTime());
            historyGroupMsgReq.setPageSize(HISTORY_MESSAGE_MAX_PAGE_SIZE);
            HistoryGroupMsgResp historyGroupMsgResp = dxClient.getHistoryGroupMsgs(historyGroupMsgReq);
            if (Objects.isNull(historyGroupMsgResp) || CollectionUtils.isEmpty(historyGroupMsgResp.getGroupMsgList())) {
                break;
            }
            searchAndFill(searchKeyMap, historyGroupMsgResp.getGroupMsgList(), searchKeyMatchMsgsMap, msgTypeList);
            if (!historyGroupMsgResp.hasMore) {
                break;
            }
            pageToken = historyGroupMsgResp.getPageToken();
        }
        //提取关键字段&格式化消息
        Map<String, List<DxMessageData>> searchKeyMsgMap = new HashMap<>();
        for (String searchKey : searchKeyMatchMsgsMap.keySet()) {
            List<GroupMsg> msgList = searchKeyMatchMsgsMap.get(searchKey);
            if (CollectionUtils.isEmpty(msgList)) {
                continue;
            }
            List<DxMessageData> dxMessageDataList = msgList.stream().map(groupMsg -> {
                switch (groupMsg.getType()) {
                    case DX_TYPE_QUOTE:
                        return DxMessageData.builder().fromUid(groupMsg.getFromUid()).msgId(groupMsg.getMsgId()).msg(extractFromQuoteMessage(groupMsg.getMessage())).msgType(groupMsg.getType()).build();
                    case DX_TYPE_TEXT:
                        return DxMessageData.builder().fromUid(groupMsg.getFromUid()).msgId(groupMsg.getMsgId()).msg(extractFromTextMessage(groupMsg.getMessage())).msgType(groupMsg.getType()).build();
                    case DX_TYPE_BASE64:
                        return DxMessageData.builder().fromUid(groupMsg.getFromUid()).msgId(groupMsg.getMsgId()).msg(extractFromBase64Message(groupMsg.getMessage())).msgType(groupMsg.getType()).build();
                }
                return null;
            }).filter(Objects::nonNull).filter(msg -> StringUtils.isNotBlank(msg.getMsg())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dxMessageDataList)) {
                searchKeyMsgMap.put(searchKey, dxMessageDataList);
            }
        }
        //补充mis号
        Set<Long> dxUidSet = searchKeyMsgMap.values().stream().flatMap(msgDataList -> msgDataList.stream().map(DxMessageData::getFromUid)).collect(Collectors.toSet());
        Map<Long, DxUserBo> dxUserBoMap = dxClient.batchGetMisByDxUid(dxUidSet);
        for (List<DxMessageData> dxMessageDataList : searchKeyMsgMap.values()) {
            for (DxMessageData dxMessageData : dxMessageDataList) {
                DxUserBo dxUserBo = dxUserBoMap.get(dxMessageData.getFromUid());
                if (Objects.nonNull(dxUserBo)) {
                    dxMessageData.setFromMis(dxUserBo.getMisId());
                    dxMessageData.setFromName(dxUserBo.getUserName());
                }
            }
        }
        Map<String, String> searchKeyMsgStrMap = new HashMap<>();
        for (String searchKey : searchKeyMsgMap.keySet()) {
            searchKeyMsgStrMap.put(searchKey, JsonUtil.toJson(searchKeyMsgMap.get(searchKey)));
        }
        return searchKeyMsgStrMap;
    }

    private void searchAndFill(Map<String, Set<String>> searchKeyMap, List<GroupMsg> groupMsgList, Map<String, List<GroupMsg>> searchKeyMatchMsgsMap, List<Integer> msgTypeList) {
        if (MapUtils.isEmpty(searchKeyMap) || CollectionUtils.isEmpty(groupMsgList) || CollectionUtils.isEmpty(msgTypeList)) {
            return;
        }
        // 过滤有效消息
        groupMsgList.stream()
                //非文本或引用，已撤销消息，机器人消息都过滤掉
                .filter(msg -> msgTypeList.contains(msg.getType()) && !msg.isIsCancel() && !Objects.equals(msg.fromPubId, 0))
                .forEach(msg -> {
                    // 获取消息内容
                    String content;
                    switch (msg.getType()) {
                        case DX_TYPE_QUOTE:
                            content = extractFromQuoteMessage(msg.getMessage());
                            break;
                        case DX_TYPE_BASE64:
                            content = extractFromBase64Message(msg.getMessage());
                            break;
                        default:
                            content = msg.getMessage();
                            break;
                    }
                    // 匹配关键词并添加到结果集
                    searchKeyMap.forEach((mainKey, keySet) -> {
                        if (keySet.stream().anyMatch(content::contains)) {
                            searchKeyMatchMsgsMap.computeIfAbsent(mainKey, k -> new ArrayList<>()).add(msg);
                        }
                    });
                });
    }

    /**
     * 从文本类型消息中提取text字段
     * @param messageJson 消息JSON字符串
     * @return 提取的text内容
     */
    private String extractFromTextMessage(String messageJson) {
        try {
            JsonNode jsonNode = JsonUtil.fromJson(messageJson);
            if (Objects.nonNull(jsonNode) && jsonNode.has("text")) {
                return jsonNode.get("text").asText();
            }
            return "";
        } catch (Exception e) {
            log.error("Failed to parse message JSON: {}", messageJson, e);
            return "";
        }
    }

    /**
     * 从引用消息中提取text字段
     * @param messageJson 消息JSON字符串
     * @return 提取的text内容
     */
    private String extractFromQuoteMessage(String messageJson) {
        try {
            JsonNode jsonNode = JsonUtil.fromJson(messageJson);
            if (Objects.nonNull(jsonNode) && jsonNode.has("searchText")) {
                return jsonNode.get("searchText").asText();
            }
            return "";
        } catch (Exception e) {
            log.error("Failed to parse message JSON: {}", messageJson, e);
            return "";
        }
    }

    /**
     * 从base64编码消息中解码并提取text字段
     * @param messageJson 消息JSON字符串
     * @return 提取的text内容
     */
    private String extractFromBase64Message(String messageJson) {
        try {
            // 1. 解析JSON获取base64编码的内容
            JsonNode jsonNode = JsonUtil.fromJson(messageJson);
            if (Objects.isNull(jsonNode) || !jsonNode.has("data")) {
                return "";
            }

            String base64Content = jsonNode.get("data").asText();
            if (StringUtils.isBlank(base64Content)) {
                return "";
            }

            // 2. Base64解码
            byte[] decodedBytes = java.util.Base64.getDecoder().decode(base64Content);
            String decodedContent = new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);

            // 3. 解析解码后的JSON，提取nodes中的text内容
            JsonNode decodedJsonNode = JsonUtil.fromJson(decodedContent);
            if (Objects.isNull(decodedJsonNode) || !decodedJsonNode.has("nodes")) {
                return decodedContent; // 如果不是预期格式，返回解码内容
            }

            // 4. 遍历nodes数组，提取所有text类型节点的内容
            StringBuilder textContent = new StringBuilder();
            JsonNode nodesArray = decodedJsonNode.get("nodes");
            if (nodesArray.isArray()) {
                for (JsonNode node : nodesArray) {
                    if (node.has("t") && "text".equals(node.get("t").asText()) && node.has("c")) {
                        if (textContent.length() > 0) {
                            textContent.append(" ");
                        }
                        textContent.append(node.get("c").asText());
                    }
                }
            }
            return textContent.toString();

        } catch (Exception e) {
            log.error("Failed to parse and decode base64 message JSON: {}", messageJson, e);
            return "";
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class DxMessageData {
        //发送人uid
        private Long fromUid;
        //发送人mis号
        private String fromMis;
        //发送人姓名
        private String fromName;
        //消息id
        private Long msgId;
        //消息内容
        private String msg;
        //消息类型
        private Integer msgType;
    }

}
