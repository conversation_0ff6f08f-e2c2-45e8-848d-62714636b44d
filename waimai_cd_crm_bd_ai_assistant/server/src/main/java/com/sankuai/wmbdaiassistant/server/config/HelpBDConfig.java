package com.sankuai.wmbdaiassistant.server.config;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.CommentTimesMccBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentStarConfigMccBo;
import java.util.ArrayList;
import java.util.HashMap;
import org.springframework.context.annotation.Configuration;

/**
 * 在线提问的Lion配置「代码迁移」
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 17:03
 */
@Configuration
public class HelpBDConfig {
    /**
     * TT 的开放接口域名
     */
    @MdpConfig("tt_api_host")
    public static String TT_API_HOST;

    /**
     * TT 的 web 页面域名
     */
    @MdpConfig("tt_web_host:http://tt.cloud.test.sankuai.com")
    public static String TT_WEB_HOST;

    /**
     * TT 超级管理员
     */
    @MdpConfig("tt_super_user:it_help")
    public static String TT_SUPER_USER;

    /**
     * 蜜蜂发送渠道。需要找消息中心的产品配置（目前是 刘振威/liuzhenwei07 ）
     */
    @MdpConfig("notice_type:172")
    public static int NOTICE_TYPE;
    /**
     * TT 的开放接口域名
     */
    @MdpConfig("ones_api_host:http://ones.vip.sankuai.com")
    public static String ONES_API_HOST;

    /**
     * TT 跳转额外参数
     * 一个 map ，key 不固定
     */
    @MdpConfig("tt_scheme_params:{\"p1\":1,\"hhh\":23}")
    public static HashMap<String, Integer> TT_SCHEME_PARAMS;

    /**
     * 无须评论的租户
     */
    @MdpConfig("moses_comment_forbidden_list:[\"1000010-9001\",\"1000010-10003\",\"1000008-5009\"]")
    public static ArrayList<String> MOSES_COMMENT_FORBIDDEN_LIST;

    /**
     * 评论频次的限制
     */
    @MdpConfig("moses_comment_max_times_config:[{\"keyList\": [],\"maxTimes\": 1}]")
    public static ArrayList<CommentTimesMccBo> MOSES_COMMENT_TIMES_CONFIG;

    /**
     * 评论配置
     */
    @MdpConfig("moses_comment_config")
    public static ArrayList<MosesCommentStarConfigMccBo> MOSES_COMMENT_CONFIG;

    /**
     * 评论新配置
     */
    @MdpConfig("moses_comment_config_new")
    public static ArrayList<MosesCommentStarConfigMccBo> MOSES_COMMENT_CONFIG_NEW;

    /**
     * 城市灰度接口开关
     */
    @MdpConfig("bd_ai_assistant_new_page_gray_switch:true")
    public static Boolean bdAIAssistantNewPageSwitch;

    /**
     * 使用BD智能助手新页面灰度业务ID列表，如果为空则表示全部开放
     */
    @MdpConfig("bd_ai_assistant_new_page_gray_bizId_list:[5001,5002,5003,5005,5006,5016,5008,1]")
    public static ArrayList<Long> bdAIAssistantNewPageGrayBizIdList;

    /**
     * TT通过三级目录挂载 rg 的方式区分 rg
     * 需要存储的tt rg
     */
    @MdpConfig("care_rg_ids:[4746,4745,4744,4743,4742,4741,4740,4739,4738,4737,4736,4735,4734,4733,4732,4731,4730,4729,4728,4727,4726,4725,4724,4723,4722,4721,4720,4719,4718,4717,4716,4715,4714,4713,4712,4711,4710,4709,4708,4707,4706,4705,4704,4703,4702,4701,4700,4699,4698,4697,4696,4695,4694,4693,4692,4691,4265,4264,4263,4025,4021,4020,4018,4017,4016,4015,4014,4013,4012,2954,2946,2944,2943,2942,2941,2940,2938,2934,2502,2645]")
    public static ArrayList<Integer> CARE_RG_ID;

    /**
     * 蜜蜂的 TT 的 rg 标示 （Resolver Group）服务组单位（需要发送蜜蜂消息）
     * TT通过三级目录挂载 rg 的方式区分 rg
     */
    @MdpConfig("bee_tt_rg_id:19961")
    public static Integer BEE_TT_RG_ID = 0;

    /**
     * 蜜蜂跳转协议
     */
    @MdpConfig("tt_mrn_url_prefix:https://beedp.sankuai.com/bee/scheme/h5schemerouter.html?new_bundle=1&biz=bfe&bundle=tt&component=ttdetail&page=Test&originURL=")
    public static String TT_MRN_URL_PREFIX;
}
