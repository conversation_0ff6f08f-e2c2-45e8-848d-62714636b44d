package com.sankuai.wmbdaiassistant.server.application.service.helpbd.mq;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerAuthException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.ServiceWrapperException;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.OnesInfoBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtTicketBo;
import com.sankuai.wmbdaiassistant.server.application.dto.TtTicketChangeDto;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.TtTicketManager;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.NoticeWrapper;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.OnesWrapper;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.TtWrapper;
import java.net.URLEncoder;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 消费 TT 的 Mafka 消息
 * 当 TT 工单更新的时候给对应的提问题的 BD 在蜜蜂上发消息
 * st 环境关闭消费 tt mq,因为 tt 的 st 没有这个 topic
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnExpression("${tt.mq.active:false} == true")
@Configuration
public class TtTicketChangeListener implements IMessageListener<String> {

    private static final String NAMESPACE = "octo";
    private static final String TOPIC = "TTPublicMessage";
    private static final String GROUP = "TTPublicMessage.bee";

    private static final String MSG_PATTERN =
            "工单：{}<br/>" +
                    "状态：{} -> {}<br/>  <br/>" +
                    "<a href=\"{}{}\">点击查看详情</a>";

    private static final String MSG_TITLE = "TT工单状态通知";

    public static final String APP_KEY = "com.sankuai.waimaimka.kaservice.skysea";

    @Resource
    private TtWrapper ttWrapper;

    @Resource
    private OnesWrapper onesWrapper;

    @Resource
    private TtTicketManager ttTicketManager;

    @Resource
    private NoticeWrapper noticeWrapper;

    @Resource
    private WmEmployService.Iface wmEmployService;
    @Bean(initMethod = "start", destroyMethod = "close")
    public MafkaConsumer ttTicketChangeConsumer() {
        log.info("tt consumer start");
        MafkaConsumer mafkaConsumer = new MafkaConsumer();
        mafkaConsumer.setNamespace(NAMESPACE);
        mafkaConsumer.setAppkey(APP_KEY);
        mafkaConsumer.setTopic(TOPIC);
        mafkaConsumer.setGroup(GROUP);
        mafkaConsumer.setListener(this);
        mafkaConsumer.setClassName(String.class.getName());
        return mafkaConsumer;
    }


    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        String mqMsg = message.getBody().toString();
        log.info("TtTicketChangeListener mqMsg: {}", mqMsg);

        try {

            TtTicketChangeDto ttTicketChangeDto = JSONObject.parseObject(mqMsg, TtTicketChangeDto.class);

            if (Objects.isNull(ttTicketChangeDto.getTicketId())) {
                log.error("tt mq msg hasn't ticketId. mqMsg: {}", mqMsg);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 获取 tt 完整工单
            TtTicketBo ticket;
            try {
                ticket = ttWrapper.getTicket(ttTicketChangeDto.getTicketId());
            } catch (RpcServerAuthException e) {
                // 不是蜜蜂的工单，而且是私有工单。忽略即可。
                log.info(e.getMessage());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 查询 tt 关联的 ones 状态（如果有的话）
            try {
                String onesId = ticket.getOnesId();
                if (StringUtils.isNotEmpty(onesId)) {
                    OnesInfoBo onesInfo = onesWrapper.getOnesInfo(onesId);
                    ticket.setOnesStatus(onesInfo.getState());
                }
            } catch (RpcServerException e) {
                // 这个地方可以忽略，因为有补偿任务
                log.warn("查询 tt 关联 ones 状态失败", e);
            }


            // 只处理蜜蜂的 tt 工单
            if (!HelpBDConfig.CARE_RG_ID.contains(ticket.getRgId())) {
                log.info("Ignore tt ticket duo to not belong to bee. ticketId:{}, rgId:{}", ticket.getTicketId(), ticket.getRgId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 如果是创建工单 只用插入数据
            if (ttTicketChangeDto.isCreate()) {
                ttTicketManager.create(ticket);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 如果是删除工单 只用更新库
            if (ttTicketChangeDto.isDelete()) {
                ttTicketManager.update(ticket);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 如果是更新工单 发送消息
            if (ttTicketChangeDto.isUpdate()) {
                // 数据库老的 tt 工单
                TtTicketBo oldTicket = ttTicketManager.queryByTicketId(ticket.getTicketId());

                // 如果老的工单没有，就先插入数据
                if (Objects.isNull(oldTicket)) {
                    ttTicketManager.create(ticket);
                } else {
                    ttTicketManager.update(ticket);
                }

                // 给蜜蜂推消息
                if (Objects.nonNull(oldTicket) &&
                        HelpBDConfig.BEE_TT_RG_ID.equals(ticket.getRgId()) &&
                        ticket.getModifyTime().after(oldTicket.getModifyTime()) &&
                        (!oldTicket.getState().equals(ticket.getState()))) {

                    String ticketName = ticket.getName();
                    String oldState = oldTicket.getState();
                    String newState = ticket.getState();
                    String encodeUrl = URLEncoder.encode(ttWrapper.getTicketInfoUrl(ticket.getTicketId()), Charsets.UTF_8.name());
                    String msg = StringUtil.of(MSG_PATTERN, ticketName, oldState, newState, HelpBDConfig.TT_MRN_URL_PREFIX, encodeUrl);

                    // 这个地方可能会超时之类的异常，但是对应的数据库应该更新
                    try {
                        WmEmploy wmEmploy = wmEmployService.getByMisId(ticket.getCreatorMis());
                        if (Objects.isNull(wmEmploy)) {
                            throw new ServiceWrapperException(StringUtil.of("can't find wmEmploy by misId: {}", ticket.getCreatorMis()));
                        }
                        int uid = wmEmploy.getUid();
                        noticeWrapper.send2Bee(uid, MSG_TITLE, msg);
                    } catch (Exception e) {
                        log.error("RPC error", e);
                    }
                }

                return ConsumeStatus.CONSUME_SUCCESS;
            }

            log.error("tt msg can't analysis type:{}", ttTicketChangeDto.getType());
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("TtTicketChangeListener msg: {}", mqMsg, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

}
