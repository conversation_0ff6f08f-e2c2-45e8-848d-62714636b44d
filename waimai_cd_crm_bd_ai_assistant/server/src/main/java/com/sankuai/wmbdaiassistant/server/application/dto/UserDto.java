package com.sankuai.wmbdaiassistant.server.application.dto;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeUserDto;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-02-28 13:41
 */
@Data
public class UserDto {

    private Long uid;

    private String misId;

    private String name;

    private String email;

    public static UserDto of(User user) {
        ParamCheckUtil.notNull(user,"用户为空");
        UserDto userDto = new UserDto();
        userDto.setUid((long) user.getId());
        userDto.setMisId(user.getLogin());
        userDto.setName(user.getName());
        userDto.setEmail(user.getEmail());
        return userDto;
    }

    public static UserDto of(BeeUserDto beeUserDto) {
        ParamCheckUtil.notNull(beeUserDto,"用户为空");
        return EnBeanUtils.copy(beeUserDto, UserDto::new);
    }
}
