package com.sankuai.wmbdaiassistant.server.config;

import com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.service.WmNoticeThriftService;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Thrift客户端配置
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/23 19:13
 */
@Configuration
@Slf4j
public class ThriftClientConfig {

    private static final int TIMEOUT = 3000;

    private static final String APP_KEY = "com.sankuai.wmbdaiassistant.server";

    private static final String APP_REMOTE_KAY = "com.sankuai.waimai.service.crmnotice";
    private static final int PORT = 8740;

    @PostConstruct
    public void init() {
        log.info("{} loading ...", this.getClass().getName());
    }

    /**
     * 通知中心
     * @return
     * @throws Exception
     */
    @Bean
    public WmNoticeThriftService.Iface wmNoticeThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setAppKey(APP_KEY);
        proxy.setRemoteAppkey(APP_REMOTE_KAY);
        proxy.setServiceInterface(WmNoticeThriftService.class);
        proxy.setRemoteServerPort(PORT);
        proxy.setTimeout(TIMEOUT);
        //初始化实例
        proxy.afterPropertiesSet();
        //获取代理对象
        return (WmNoticeThriftService.Iface) proxy.getObject();
    }

}
