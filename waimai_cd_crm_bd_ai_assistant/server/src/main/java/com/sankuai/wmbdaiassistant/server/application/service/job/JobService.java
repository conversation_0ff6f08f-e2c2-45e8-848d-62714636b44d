package com.sankuai.wmbdaiassistant.server.application.service.job;

import com.sankuai.wmbdaiassistant.api.response.JobListResponseDto;
import com.sankuai.wmbdaiassistant.api.response.RunningJobCountResponseDto;

import java.util.Date;

/**
 * 任务接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/07/08
 */
public interface JobService {

    /**
     * 获取任务列表
     *
     * @param mis
     * @param sessionId
     * @param jobType
     * @param startDate
     * @param endDate
     * @return
     */
    JobListResponseDto getJobs(String mis, Long sessionId, String jobType, Date startDate, Date endDate);

    /**
     * 获取商家诊断正在运行的任务数量
     *
     * @param mis
     * @param startDate
     * @param endDate
     * @return
     */
    RunningJobCountResponseDto getPoiDiagnosisRunningJobsNum(String mis, Date startDate, Date endDate);
}
