package com.sankuai.wmbdaiassistant.server.application.bo.helpbd;

import com.sankuai.wmbdaiassistant.domain.enums.helpbd.EntryTrackingTypeEnum;
import java.util.Date;
import lombok.Data;

/**
 * 入口埋点
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-02 10:40
 */
@Data
public class EntryTrackingBo {

    /**
     * 来源
     */
    private EntryTrackingTypeEnum source;

    /**
     * 机器人类型，0：摩西机器人 1：智能客服
     */
    private Integer robotType;

    /**
     * 摩西机器人ID/智能客服url
     */
    private String robotId;

    /**
     * 评论用户的 uid
     */
    private Long uid;

    /**
     * 评论用户的 misID
     */
    private String misId;

    /**
     * 租户信息
     */
    private TenantBo tenant;

    /**
     * 创建时间
     */
    private Date createdTime;

}
