package com.sankuai.wmbdaiassistant.server.application.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * Tt 工单变更 Mafka 的数据传输对象
 * 这个地方的 type 不要直接解析成枚举，否则 fastJson 会报错
 *
 * <AUTHOR>
 */
@Data
public class TtTicketChangeDto {

    /**
     * 创建的时候 ticketId 在 data 里面
     */
    public static final String TICKET_ID_PATH = "$.ticketId";

    /**
     * TT的 id，唯一标识
     */
    private Integer ticketId;

    /**
     * 数据生产者
     */
    private String operator;

    /**
     * 当前数据权限
     */
    private String permission;

    /**
     * 数据类型
     */
    private String type;

    /**
     * 具体数据
     */
    private String data;

    public boolean isCreate() {
        return TtMsgType.MSG_CREATE.getName().equals(type);
    }

    public boolean isUpdate() {
        return TtMsgType.MSG_UPDATE.getName().equals(type);
    }

    public boolean isDelete() {
        return TtMsgType.MSG_DELETE.getName().equals(type);
    }

    public void setData(String data) {
        this.data = data;
        // 如果是 MSG_CREATE 消息，ticketId 在 data 里面，需要取出来
        if (Objects.isNull(this.ticketId) && JSONPath.contains(data, TtTicketChangeDto.TICKET_ID_PATH)) {
            this.ticketId = Integer.valueOf(JSONPath.eval(data, TtTicketChangeDto.TICKET_ID_PATH).toString());
        }
    }

    @AllArgsConstructor
    @Getter
    public enum TtMsgType {
        /**
         * 工单新建
         */
        MSG_CREATE("MSG_CREATE"),
        /**
         * 工单更新
         */
        MSG_UPDATE("MSG_UPDATE"),
        /**
         * 工单删除
         */
        MSG_DELETE("MSG_DELETE"),
        ;

        private final String name;
    }

    public static void main(String[] args) {
        System.out.println(JSON.parseObject("{\"data\":{\"assigned\":\"wangjindong02\",\"breakResolve\":false,\"breakResponse\":false,\"categoryId\":6,\"categoryName\":\"服务运维\",\"ccUsers\":\"chenbaisheng,wangyajing,huyuan03,wangjunfeng05,wangyukun05,chenshi09\",\"createdAt\":1569403237858,\"createdBy\":\"huyuan03\",\"desc\":\"[P2]微信B扫C失败-微信B扫C失败<br>日环比：近2min平均值135.0，上涨32.4%，超过30%<br>当前值：143.0<br>应用：com.sankuai.pay.postrade | BARCODE-FY_WX-PAY<br>所属部门：金融服务平台/支付事业部/支付平台部/研发组/交易收银组/线下收单交易研发组/交易支撑组\\n接收人：huyuan03,wangjindong02,wangjunfeng05,wangyajing,wangyukun05...<br>数据时间：2019-09-25 17:18<br>告警时间：2019-09-25 17:20:32<br>详情：<a href='http://tw.inf.test.sankuai.com/#/metric/166972?beg=201909251620&end=201909251722&event_id=75356'>PC查看</a> | <a href='https://tw-test-app.sankuai.com/#/metric/166972?event_id=75356'>手机查看</a>\",\"id\":488575,\"itemId\":586,\"itemName\":\"测试SRE\",\"keyWord\":\"TT-2948335\",\"name\":\"[P2]微信B扫C失败-微信B扫C失败, 当前值:143.0, 日环比：近2min平均值135.0，上涨32.4%，超过30%\",\"permission\":\"public\",\"reporter\":\"it_skynet\",\"rgId\":122,\"sla\":\"S4\",\"source\":\"ticket.tw-web\",\"state\":\"未处理\",\"ticketType\":\"服务故障\",\"typeId\":137,\"typeName\":\"天网\"},\"operator\":\"huyuan03\",\"permission\":\"public\",\"type\":\"MSG_CREATE\"}", TtTicketChangeDto.class));
    }

}
