package com.sankuai.wmbdaiassistant.server.application.service.dsl.impl;

import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.JobItemEntityTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.model.JobModel;
import com.sankuai.wmbdaiassistant.domain.model.JobTriggerModel;
import com.sankuai.wmbdaiassistant.domain.repository.JobItemRepository;
import com.sankuai.wmbdaiassistant.domain.repository.JobRepository;
import com.sankuai.wmbdaiassistant.domain.transaction.TransactionHelper;
import com.sankuai.wmbdaiassistant.infrastructure.integration.agent.poidiagnose.PoiDiagnoseAgentClient;
import com.sankuai.wmbdaiassistant.infrastructure.integration.agent.poidiagnose.dto.PoiDiagnosticReadyResponseDto;
import com.sankuai.wmbdaiassistant.infrastructure.integration.poi.PoiClient;
import com.sankuai.wmbdaiassistant.server.application.dto.PoiDiagnosticReadyDto;
import com.sankuai.wmbdaiassistant.server.application.service.dsl.PoiDiagnoseDslService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


/**
 * 商家诊断技能（供 DSL 调用）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-11
 */
@Slf4j
@Service("PoiDiagnoseDslService")
public class PoiDiagnoseDslServiceImpl implements PoiDiagnoseDslService {

    private static final String TRIGGER_DIAGNOSE_CONTENT_TEMPLATE = "我要查看【%s】（%s）的诊断结果";

    @Resource
    private PoiDiagnoseAgentClient poiDiagnoseAgentClient;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobItemRepository jobItemRepository;

    @Resource
    private PoiClient poiClient;

    @Resource
    private TransactionHelper transactionHelper;

    
    

    @Override
    public Map<Long, PoiDiagnosticReadyDto> triggerPoiDiagnostic(List<Long> poiIdList, Integer uid, String mis) {
        ParamCheckUtil.notNull(poiIdList, "商家列表为空");
        ParamCheckUtil.notNull(uid, "用户id为空");

        Map<Long, PoiDiagnosticReadyDto> poiDiagnosticStatusMap = new HashMap<>();

        // 单个商家诊断处理
        if(poiIdList.size() == 1) {
            Long poiId = poiIdList.get(0);
            handleSinglePoiDiagnostic(poiId, uid, mis, poiDiagnosticStatusMap);
            return poiDiagnosticStatusMap;
        }

        // 批量商家诊断处理
        handleBatchPoiDiagnostic(poiIdList, uid, mis, poiDiagnosticStatusMap);
        return poiDiagnosticStatusMap;
    }

    

    private void handleSinglePoiDiagnostic(Long poiId, Integer uid, String mis, Map<Long, PoiDiagnosticReadyDto> statusMap) {
        // 查询诊断状态
        PoiDiagnosticReadyResponseDto diagnosticReadyData = poiDiagnoseAgentClient.queryPoiDiagnosisDataReady(poiId, uid);

        // 处理诊断结果
        boolean isNotAddAsyncJob = processPoiDiagnostic(poiId, uid, diagnosticReadyData, statusMap);
        if(isNotAddAsyncJob) {
            return;
        }

        // 创建init状态的任务
        transactionHelper.doInTransactionWithOutResult(() -> {
            addAsyncJob(poiId, uid, mis, statusMap);
        });

    }

    private void handleBatchPoiDiagnostic(List<Long> poiIdList, Integer uid, String mis, Map<Long, PoiDiagnosticReadyDto> statusMap) {
        for(Long poiId : poiIdList) {
            PoiDiagnosticReadyResponseDto diagnosticReadyData = poiDiagnoseAgentClient.queryPoiDiagnosisDataReady(poiId, uid);
            processPoiDiagnostic(poiId, uid, diagnosticReadyData, statusMap);
        }


        transactionHelper.doInTransactionWithOutResult(() -> {
            Long jobId = jobRepository.insert(buildPoiDiagnoseJobModel(uid, mis));
            poiIdList.forEach(poiId -> addAsyncJobItem(jobId, uid, mis, poiId, statusMap));
        });


    }

    // 返回是否添加异步任务
    private boolean processPoiDiagnostic(Long poiId, Integer uid, PoiDiagnosticReadyResponseDto diagnosticReadyData,
                                         Map<Long, PoiDiagnosticReadyDto> statusMap) {
        // 查询诊断为null
        if(diagnosticReadyData == null) {
            statusMap.put(poiId, buildPoiDiagnosticExceptionDto("查询商家诊断数据是否就绪失败，请稍后重试"));
            return true;
        }

        // 商家不支持诊断
        if(diagnosticReadyData.isUnSupportDiagnose()) {
            statusMap.put(poiId, buildPoiDiagnosticExceptionDto(diagnosticReadyData.getUnsupportedDiagnoseReason()));
            return true;
        }

        // 诊断数据就绪
        if(diagnosticReadyData.isDataReady()) {
            statusMap.put(poiId, buildPoiDiagnosticReadyDto());
            return true;
        }

        // 未触发诊断or系统诊断错误 -》 触发诊断
        boolean isTriggerSuccess = poiDiagnoseAgentClient.triggerPoiDiagnosis(poiId, uid);
        if(!isTriggerSuccess) {
            statusMap.put(poiId, buildPoiDiagnosticExceptionDto("触发商家诊断请求失败，请稍后重试"));
            return true;
        }

        statusMap.put(poiId, buildPoiDiagnosticTriggerSuccessDto());
        return false;
    }

    private PoiDiagnosticReadyDto buildPoiDiagnosticReadyDto() {
        return PoiDiagnosticReadyDto.builder()
                .dataReady(Boolean.TRUE)
                .hasException(Boolean.FALSE)
                .build();
    }


    private PoiDiagnosticReadyDto buildPoiDiagnosticExceptionDto(String errorMessage) {
        return PoiDiagnosticReadyDto.builder()
                .dataReady(Boolean.FALSE)
                .hasException(Boolean.TRUE)
                .exceptionMessage(errorMessage)
                .build();
    }


    private PoiDiagnosticReadyDto buildPoiDiagnosticTriggerSuccessDto() {
        return PoiDiagnosticReadyDto.builder()
                .dataReady(Boolean.FALSE)
                .hasException(Boolean.FALSE)
                .build();
    }

    private void addAsyncJob(Long poiId, Integer uid, String mis, Map<Long, PoiDiagnosticReadyDto> statusMap) {
        Long jobId = jobRepository.insert(buildPoiDiagnoseJobModel(uid, mis));
        addAsyncJobItem(jobId, uid, mis, poiId, statusMap);
    }

    private void addAsyncJobItem(Long jobId, Integer uid, String mis, Long poiId, Map<Long, PoiDiagnosticReadyDto> statusMap) {
        PoiDiagnosticReadyDto readyDto = statusMap.get(poiId);
        JobStatusEnum jobStatus = determineJobStatus(readyDto);
        jobItemRepository.insert(buildPoiDiagnoseJobItemModel(jobId, uid, mis, poiId, jobStatus));
    }

    private JobStatusEnum determineJobStatus(PoiDiagnosticReadyDto readyDto) {
        if(Boolean.TRUE.equals(readyDto.getHasException())) {
            return JobStatusEnum.FAIL;
        }
        if(Boolean.TRUE.equals(readyDto.getDataReady())) {
            return JobStatusEnum.SUCCESS;
        }
        return JobStatusEnum.INIT;
    }


    private JobModel buildPoiDiagnoseJobModel(Integer uid, String mis){
        return JobModel.builder().type(JobTypeEnum.POI_DIAGNOSIS).uid(uid).mis(mis)
                .ctime(new Date()).utime(new Date()).build();
    }

    private JobItemModel buildPoiDiagnoseJobItemModel(Long jobId, Integer uid, String mis, Long poiId, JobStatusEnum status) {
        return JobItemModel.builder()
                .jobId(jobId)
                .type(JobTypeEnum.POI_DIAGNOSIS)
                .uid(uid)
                .mis(mis)
                .entityType(JobItemEntityTypeEnum.WM_POI_ID.getCode())
                .entityId(String.valueOf(poiId))
                .status(status)
                .trigger(buildTriggerModel(poiId))
                .ctime(new Date())
                .utime(new Date())
                .build();
    }

    private JobTriggerModel buildTriggerModel(Long poiId) {
        JobTriggerModel triggerModel = new JobTriggerModel();
        triggerModel.setAbilityType(1);
        triggerModel.setOperationType(2);
        triggerModel.setContent(buildTriggerContent(poiId));
        return triggerModel;
    }
        
    private String buildTriggerContent(Long poiId) {
        WmPoiAggre wmPoiAggre = poiClient.queryWmPoiAggre(poiId, Sets.newHashSet("name"));
        String poiName = Optional.ofNullable(wmPoiAggre).map(WmPoiAggre::getName).orElse("");
        return String.format(TRIGGER_DIAGNOSE_CONTENT_TEMPLATE, poiName, poiId);
    }
}









