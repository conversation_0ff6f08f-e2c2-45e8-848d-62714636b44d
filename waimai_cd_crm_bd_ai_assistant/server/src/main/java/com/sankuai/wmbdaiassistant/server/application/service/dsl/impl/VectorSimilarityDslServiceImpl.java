package com.sankuai.wmbdaiassistant.server.application.service.dsl.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.vector.VectorMatchResultModel;
import com.sankuai.wmbdaiassistant.domain.service.chat.vectordb.VectorMatchService;
import com.sankuai.wmbdaiassistant.server.application.service.dsl.VectorSimilarityDslService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 向量相似度DSL服务实现
 *
 * <AUTHOR>
 * @description 向量相似度匹配服务实现（供DSL调用）
 * @create 2024/12/24 10:00
 */
@Slf4j
@Service("VectorSimilarityDslService")
public class VectorSimilarityDslServiceImpl implements VectorSimilarityDslService {

    @Resource
    private VectorMatchService vectorMatchService;

    @MdpConfig("vector.dsl.max.matach.size:30")
    private Integer MAX_MATCH_SIZE;

    @Override
    public VectorMatchResultModel findMostSimilarPhrase(String input, List<String> phraseList) {
        // 参数校验
        ParamCheckUtil.notBlank(input, "输入字符串不能为空");
        ParamCheckUtil.notEmpty(phraseList, "短语列表不能为空");
        ParamCheckUtil.isTrue(phraseList.size() <= MAX_MATCH_SIZE, "短语列表长度不能超过" + MAX_MATCH_SIZE);

        // 如果input直接命中phraseList，直接返回
        if (phraseList != null && phraseList.contains(input)) {
            log.debug("Input '{}' directly matches phrase in list, returning directly", input);
            return VectorMatchResultModel.builder().selectedPhrase(input).matchScore(1.0).build();
        }

        // 否则委托给VectorMatchService进行向量相似度匹配
        return vectorMatchService.findMostSimilarPhrase(input, phraseList);
    }
}
