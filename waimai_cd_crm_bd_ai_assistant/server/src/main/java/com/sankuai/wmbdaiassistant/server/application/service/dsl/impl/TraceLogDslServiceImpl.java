package com.sankuai.wmbdaiassistant.server.application.service.dsl.impl;

import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.server.application.service.dsl.TraceLogDslService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 埋点日志DSL服务实现
 *
 * <AUTHOR> AI Assistant
 * @date 2025-01-27
 */
@Slf4j
@Service("TraceLogDslService")
public class TraceLogDslServiceImpl implements TraceLogDslService {

    @Resource
    private TraceLogService traceLogService;

    /**
     * 记录任务流子意图埋点
     *
     * @param sessionId 会话ID
     * @param taskSessionId 任务会话ID
     * @param msgId 消息ID
     * @param correctedIntent 订正后意图
     */
    @Override
    public void recordTaskIntentTraceLog(Long sessionId, String taskSessionId, Long msgId, String correctedIntent) {
        try {
            // 设置入口点
            String entryPoint = "dsl_api";

            // 构建埋点日志
            TraceLogModel traceLogModel = TraceLogModel.buildTaskFlowIntentCorrectionTraceLog(
                    sessionId, taskSessionId, msgId, entryPoint, correctedIntent);

            // 保存埋点日志
            traceLogService.insert(traceLogModel);

            log.info("TraceLogDslService recordTaskFlowSubIntentTrace success, sessionId: {}, msgId: {}, " +
                    "correctedIntent: {}",
                    sessionId, msgId, correctedIntent);

        } catch (Exception e) {
            log.error("TraceLogDslService recordTaskFlowSubIntentTrace error", e);
        }
    }
}
