package com.sankuai.wmbdaiassistant.server.application.converter;

import com.sankuai.wmbdaiassistant.api.response.JobItemResponseDto;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.model.JobTriggerModel;

import java.util.Objects;

/**
 *  Job相关Converter
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-08
 */
public class JobConverter {

    public static JobItemResponseDto toJobItemResponseDto(JobItemModel jobItemModel, String poiName, String poiAvator) {
        if (Objects.isNull(jobItemModel)) {
            return null;
        }

        JobItemResponseDto jobItemResponseDto = new JobItemResponseDto();
        jobItemResponseDto.setPoiId(Integer.valueOf(jobItemModel.getEntityId()));
        jobItemResponseDto.setPoiName(poiName);
        jobItemResponseDto.setPoiAvator(poiAvator);
        jobItemResponseDto.setStatus(jobItemModel.getStatus() == null ? null : jobItemModel.getStatus().getCode());
        JobTriggerModel trigger = jobItemModel.getTrigger();
        if (trigger != null) {
            jobItemResponseDto.setAbilityType(String.valueOf(trigger.getAbilityType()));
            jobItemResponseDto.setOperationType(String.valueOf(trigger.getOperationType()));
            jobItemResponseDto.setContent(trigger.getContent());
        }

        return jobItemResponseDto;
    }
}
