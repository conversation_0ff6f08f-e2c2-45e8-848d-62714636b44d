package com.sankuai.wmbdaiassistant.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.domain.enums.JobStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.repository.JobItemRepository;
import com.sankuai.wmbdaiassistant.infrastructure.integration.agent.poidiagnose.PoiDiagnoseAgentClient;
import com.sankuai.wmbdaiassistant.infrastructure.integration.agent.poidiagnose.dto.PoiDiagnosticReadyResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 自动更新任务项状态
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-15
 */
@Slf4j
@CraneConfiguration
public class AutoUpdateJobItemStatusJob {

    @Resource
    private JobItemRepository jobItemRepository;

    @Resource
    private PoiDiagnoseAgentClient poiDiagnoseAgentClient;

    @Crane("update-poi-diagnose-job-status-job")
    public void updatePoiDiagnoseJobStatus() {
        log.info("update-poi-diagnose-job-status-job start");

        List<JobItemModel> jobItemModels = jobItemRepository.findByTypeAndStatus(JobTypeEnum.POI_DIAGNOSIS.getCode(), JobStatusEnum.INIT.getCode());
        if (CollectionUtils.isEmpty(jobItemModels)) {
            log.info("update-poi-diagnose-job-status-job end, no init job item to update");
            return;
        }

        log.info("update-poi-diagnose-job-status-job, {}个 init jobItem to update", jobItemModels.size());
        for (JobItemModel jobItemModel : jobItemModels) {
            // 1天前创建的任务项更新为fail
            if (jobItemModel.getCtime().before(DateUtil.getDaysAgo(1))) {
                updateJobItemStatus(jobItemModel, JobStatusEnum.FAIL);
                continue;
            }

            PoiDiagnosticReadyResponseDto diagnosticReadyDto = poiDiagnoseAgentClient.queryPoiDiagnosisDataReady(
                    Long.valueOf(jobItemModel.getEntityId()), jobItemModel.getUid());

            if (diagnosticReadyDto == null) {
                continue;
            }

            if (diagnosticReadyDto.isDataReady()) {
                updateJobItemStatus(jobItemModel, JobStatusEnum.SUCCESS);
            } else if (diagnosticReadyDto.isDataAbnormal()) {
                updateJobItemStatus(jobItemModel, JobStatusEnum.FAIL);
            }
        }

        log.info("update-poi-diagnose-job-status-job end");
    }

    private void updateJobItemStatus(JobItemModel jobItemModel, JobStatusEnum jobStatus) {
        jobItemModel.setStatus(jobStatus);
        jobItemModel.setUtime(new Date());
        jobItemRepository.update(jobItemModel);
    }
}
















