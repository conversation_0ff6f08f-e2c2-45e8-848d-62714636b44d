package com.sankuai.wmbdaiassistant.server.application.service.dsl.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.waimai.thrift.domain.AuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditTask;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.domain.bo.SessionBo;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceConfigManager;
import com.sankuai.wmbdaiassistant.domain.config.SessionSourceEntity;
import com.sankuai.wmbdaiassistant.domain.enums.PoiRejectTaskAnswerTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.model.friday.FridayAppParam;
import com.sankuai.wmbdaiassistant.domain.service.chat.cache.SessionCacheService;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.infrastructure.integration.audit.AuditClient;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.FridayAuthClient;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.FridayClient;
import com.sankuai.wmbdaiassistant.infrastructure.integration.llm.friday.response.FridayAppResponse;
import com.sankuai.wmbdaiassistant.server.application.bo.PoiRejectSuggestionRequest;
import com.sankuai.wmbdaiassistant.server.application.service.dsl.RejectTaskDslServiceV2;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商家驳回任务服务V2
 *
 * <AUTHOR>
 * @date 2025-07-10 19:50
 */
@Slf4j
@Service("RejectTaskDslServiceV2")
public class RejectTaskDslServiceV2Impl implements RejectTaskDslServiceV2 {

    @Resource
    private SessionCacheService sessionCacheService;

    @Resource
    private SessionSourceConfigManager sessionSourceConfigManager;

    @Resource
    private FridayClient fridayClient;

    @Resource
    private FridayAuthClient fridayAuthClient;

    @Resource
    private AuditClient auditClient;

    @Resource
    private TraceLogService traceLogService;

    private static final String ALL_AUDIT_STATUS = String.join(",", String.valueOf(AuditStatusEnum.REJECT.getValue()), String.valueOf(AuditStatusEnum.PASS.getValue()));

    private static final String ALL_TASK_NO_SUGGESTION_TEXT = "实在抱歉，针对该商家的驳回任务，小蜜暂未查询到修改方案。正在加急接入中，敬请期待~";

    private static final String SUGGESTION_LIST_KEY = "suggestionList";

    private static final String ALL_TASK_NO_SUGGESTION_KEY  = "allTaskNoSuggestion";

    private static final Integer BASE_INFO_MAIN_TYPE = 1;

    private static final Integer QUALITY_INFO_MAIN_TYPE = 2;


    @MdpConfig("poi_reject_suggestion_v2_friday_app_id:1944694953519386675")
    private String fridayAppId;



    @Override
    public Map<String, Object> fetchPoiRejectTaskSuggestion(PoiRejectSuggestionRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        if (Objects.isNull(request)) {
            return resultMap;
        }
        Long wmPoiId = request.getWmPoiId();
        Long taskId = request.getTaskId();
        Integer timeout = request.getTimeout();

        SessionBo sessionBo = sessionCacheService.fetch(request.getSessionId());
        SessionSourceEntity sessionSourceEntity = SessionSourceConfigManager.UNKNOWN_SOURCE;
        String mis = "";
        if (Objects.nonNull(sessionBo)) {
            sessionSourceEntity = sessionSourceConfigManager.getByCode(sessionBo.getSource());
            mis = sessionBo.getMis();
        }

        if (taskId == null) {
            try {
                return fetchPoiAllRejectTaskSuggestion(request.getSessionId(), wmPoiId, sessionSourceEntity.getPlatformCode(), mis, timeout);
            } catch (Exception e) {
                log.error("fetchPoiAllRejectTaskSuggestion error", e);
                return resultMap;
            }
        }
        LlmResponseDataV2 llmResponseDataV2 = fetchSuggestionByTaskId(request.getSessionId(), taskId, sessionSourceEntity.getPlatformCode(), mis, timeout);
        if (Objects.isNull(llmResponseDataV2)) {
            return resultMap;
        }
        WmAuditTask wmAuditTask = auditClient.getPoiIdByTaskID(taskId.intValue());
        if (Objects.nonNull(wmAuditTask)) {
            llmResponseDataV2.setRejectTime(DateUtil.dateFormat(new Date(wmAuditTask.getUtime()* 1000L), DateUtil.DEFAULT_LONG_FORMAT));
        } else {
            llmResponseDataV2.setRejectTime("未知，以审核系统为准");
        }
        //审核任务类型打点
        ExecutorUtil.safeExecute(() -> {
            traceLogService.insert(
                    TraceLogModel.buildPoiRejectTaskTypeTraceLog(sessionBo.getSessionId(), Collections.singletonList(String.valueOf(wmAuditTask.getBizType()))));
        });
        //回答类型打点
        ExecutorUtil.safeExecute(() -> traceLogService.insert(
                TraceLogModel.buildPoiRejectTaskAnswerTypeTraceLog(sessionBo.getSessionId(), Collections.singletonList(llmResponseDataV2.getAnswerType()))));
        resultMap.put(SUGGESTION_LIST_KEY, Collections.singletonList(SingleTaskSuggestion.builder()
                .rejectTime(llmResponseDataV2.getRejectTime())
                .bizTypeName(llmResponseDataV2.getBizTypeName())
                .markdownContent(llmResponseDataV2.getSuggestionMarkdown())
                .fixUrl(llmResponseDataV2.getFixUrl())
                .build()));
        return resultMap;
    }

    private Map<String, Object> fetchPoiAllRejectTaskSuggestion(Long sessionId, Long wmPoiId, String sourcePlatformCode, String mis, Integer timeout) {
        Map<String, Object> resultMap = new HashMap<>();
        List<WmAuditTask> allAuditTaskList = auditClient.getTaskByPoiAndStatus(wmPoiId.intValue(), ALL_AUDIT_STATUS);
        if (CollectionUtils.isEmpty(allAuditTaskList)) {
            return resultMap;
        }
        //驳回原因相同的审核任务，取提交时间最晚的
        List<WmAuditTask> rejectAuditTaskList = getCurrentRejectTask(allAuditTaskList);
        rejectAuditTaskList = auditClient.SuppleTaskRejectReason(rejectAuditTaskList);
        log.info("fetchPoiAllRejectTaskSuggestion getCurrentRejectTask result:{}", JsonUtil.toJson(rejectAuditTaskList));
        List<String> bizTypeList = rejectAuditTaskList.stream().map(task -> String.valueOf(task.getBizType())).collect(Collectors.toList());
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildPoiRejectTaskTypeTraceLog(sessionId, bizTypeList)));

        List<LlmResponseDataV2> turingResponseDataList = new ArrayList<>();
        for (WmAuditTask wmAuditTask : rejectAuditTaskList) {
            LlmResponseDataV2 llmResponseDataV2 = fetchSuggestionByTaskId(sessionId, (long) wmAuditTask.getId(), sourcePlatformCode, mis, timeout);
            if (Objects.nonNull(llmResponseDataV2)) {
                llmResponseDataV2.setRejectTime(DateUtil.dateFormat(new Date(wmAuditTask.getUtime()* 1000L), DateUtil.DEFAULT_LONG_FORMAT));
                turingResponseDataList.add(llmResponseDataV2);
            }
        }
        if (CollectionUtils.isEmpty(turingResponseDataList)) {
            return resultMap;
        }

        List<LlmResponseDataV2> turingResponseNormalList = turingResponseDataList.stream()
                .filter(turingRes -> !PoiRejectTaskAnswerTypeEnum.isBackUp(turingRes.getAnswerType())).collect(Collectors.toList());

        //回答类型打点
        ExecutorUtil.safeExecute(() -> traceLogService.insert(
                TraceLogModel.buildPoiRejectTaskAnswerTypeTraceLog(
                        sessionId, turingResponseDataList.stream().map(LlmResponseDataV2::getAnswerType).distinct().collect(Collectors.toList()))));
        if (CollectionUtils.isEmpty(turingResponseNormalList)) {
            //正常回复为空，则均为兜底回复，直接输出兜底内容
            resultMap.put(ALL_TASK_NO_SUGGESTION_KEY, ALL_TASK_NO_SUGGESTION_TEXT);
        } else {
            List<SingleTaskSuggestion> taskSuggestionList = new ArrayList<>();
            for (LlmResponseDataV2 turingResponseData : turingResponseDataList) {
                taskSuggestionList.add(SingleTaskSuggestion.builder()
                        .rejectTime(turingResponseData.getRejectTime())
                        .bizTypeName(turingResponseData.getBizTypeName())
                        .markdownContent(StringUtil.escape(turingResponseData.getSuggestionMarkdown()))
                        .fixUrl(turingResponseData.getFixUrl())
                        .build());
            }
            resultMap.put(SUGGESTION_LIST_KEY, taskSuggestionList);

        }
        return resultMap;
    }

    /**
     * 对审核任务列表按照驳回原因去重,保留最新提交的记录
     * @param taskList 原始审核任务列表
     * @return 去重后的审核任务列表
     */
    private List<WmAuditTask> getCurrentRejectTask(List<WmAuditTask> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return taskList;
        }

        // 使用Map<Byte, List<WmAuditTask>>来存储相同bizType的任务
        Map<Byte, List<WmAuditTask>> bizTypeToTasksMap = new HashMap<>();

        for (WmAuditTask task : taskList) {
            Byte bizType = task.getBizType();
            bizTypeToTasksMap.computeIfAbsent(bizType, k -> new ArrayList<>()).add(task);
        }

        return bizTypeToTasksMap.values().stream()
                .map(tasks -> // 先按submitTime降序排序，submitTime相同则按id降序排序
                        // 只取每组排序后的第一个元素
                        tasks.stream().min((t1, t2) -> {
                                    int submitTimeCompare = Long.compare(t2.getSubmitTime(), t1.getSubmitTime());
                                    return submitTimeCompare != 0 ? submitTimeCompare : Integer.compare(t2.getId(), t1.getId());
                                })
                        .orElse(null))
                .filter(Objects::nonNull)
                .filter(task -> task.getStatus() == AuditStatusEnum.REJECT.getValue())
                .sorted((t1, t2) -> Long.compare(t2.getUtime(), t1.getUtime())) // 按驳回时间从大到小排序
                .collect(Collectors.toList());
    }

    private LlmResponseDataV2 fetchSuggestionByTaskId(Long sessionId, Long taskId, String sourcePlatformCode, String mis, Integer timeout) {
        Map<String, String> ext = new HashMap<>();
        ext.put("taskId", String.valueOf(taskId));
        ext.put("sourcePlatform", sourcePlatformCode);
        ext.put("sessionId", String.valueOf(sessionId));
        String token = fridayAuthClient.getAccessToken();
        // 构造Friday请求参数
        FridayAppParam fridayParam = FridayAppParam.builder()
                .appId(fridayAppId)
                .userId(mis)
                .userType("MIS")
                .accessToken(token)
                .utterances(Collections.singletonList("1"))
                .businessInfo(ext)
                .timeout(timeout != null ? timeout.longValue() : 30000L)
                .build();

        // 调用Friday服务
        FridayAppResponse fridayResponse = fridayClient.chatWithApp(fridayParam);
        if (Objects.isNull(fridayResponse) || fridayResponse.getCode() != FridayAppResponse.SUCCESS_CODE) {
            return null;
        }
        // 尝试从metaInfo中解析workflow输出
        return parseFromMetaInfo(fridayResponse);
    }

    /**
     * 从Friday响应的metaInfo中解析数据
     */
    private LlmResponseDataV2 parseFromMetaInfo(FridayAppResponse fridayResponse) {
        FridayAppResponse.FridayAppResponseData data = fridayResponse.getData();
        if (data == null) {
            return null;
        }
        FridayAppResponse.FridayMetaInfo metaInfo = data.getMetaInfo();
        if (metaInfo == null) {
            return null;
        }
        List<FridayAppResponse.FridayWorkflow> workflow = metaInfo.getWorkflow();
        if (CollectionUtils.isEmpty(workflow)) {
            return null;
        }
        // 取第一个workflow节点的outputs
        FridayAppResponse.FridayWorkflow firstWorkflow = workflow.get(0);
        List<FridayAppResponse.FridayOutput> outputs = firstWorkflow.getOutputs();
        if (CollectionUtils.isEmpty(outputs)) {
            return null;
        }
        // 解析outputs中的字段
        LlmResponseDataV2 responseData = new LlmResponseDataV2();
        for (FridayAppResponse.FridayOutput output : outputs) {
            String name = output.getName();
            Object value = output.getValue();

            if ("suggestionMarkdown".equals(name) && value != null) {
                responseData.setSuggestionMarkdown(String.valueOf(value));
            } else if ("bizMainType".equals(name) && value != null) {
                responseData.setBizMainType(Integer.valueOf(String.valueOf(value)));
                responseData.setBizTypeName(Objects.equals(BASE_INFO_MAIN_TYPE, responseData.getBizMainType()) ? "基本信息" : "资质信息");
            } else if ("answerType".equals(name) && value != null) {
                responseData.setAnswerType(Integer.valueOf(String.valueOf(value)));
            } else if ("fixUrl".equals(name) && value != null) {
                responseData.setFixUrl(String.valueOf(value));
            }
        }
        return responseData;
    }

    @Data
    //测试暂时public
    public static class LlmResponseDataV2 {
        //markdown格式的修改建议
        private String suggestionMarkdown;
        // biz大类 1-基本信息  2-资质信息
        private Integer bizMainType;
        //修改建议，枚举PoiRejectTaskAnswerTypeEnum
        private Integer answerType;
        private String fixUrl;

        private String rejectTime;
        private String bizTypeName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SingleTaskSuggestion {
        private String rejectTime;
        private String bizTypeName;
        private String markdownContent;
        private String fixUrl;
    }

}
