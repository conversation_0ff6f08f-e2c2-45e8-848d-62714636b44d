package com.sankuai.wmbdaiassistant.server.application.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TtApiBaseDto<T> {
    private static final Integer SUCCESS = 200;
    private static final Integer AUTH_FORBIDDEN = 400;

    private Integer code;
    private String message;
    private T data;

    public boolean isSuccess() {
        return SUCCESS.equals(code);
    }

    public boolean isFailure() {
        return !isSuccess();
    }

    public boolean isAuthForbidden() {
        return AUTH_FORBIDDEN.equals(code);
    }
}
