package com.sankuai.wmbdaiassistant.server.application.service.dataset.impl;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.common.exception.BizErrorEnum;
import com.sankuai.wmbdaiassistant.domain.enums.FragmentStateEnum;
import com.sankuai.wmbdaiassistant.domain.enums.WikiStateEnum;
import com.sankuai.wmbdaiassistant.domain.model.FragmentCoreModel;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.model.WikiModel;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentCoreRepository;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentRepository;
import com.sankuai.wmbdaiassistant.domain.repository.WikiRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentCoreQuery;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentQuery;
import com.sankuai.wmbdaiassistant.domain.repository.query.WikiQuery;
import com.sankuai.wmbdaiassistant.server.application.service.dataset.KnowledgeChangeService;
import com.sankuai.wmbdaiassistant.server.mq.event.WikiEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * wiki 变更处理实现类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-23 14:50
 */
@Slf4j
@Service
public class KnowledgeChangeServiceImpl implements KnowledgeChangeService {

    @Resource
    private WikiRepository wikiRepository;

    @Resource
    private FragmentRepository fragmentRepository;

    @Resource
    private FragmentCoreRepository fragmentCoreRepository;

    @MdpConfig("es.data.read.internal.second:2")
    private Integer esDataReadInternalSecond;

    @Resource(name = "knowledgeChangeMqProducer")
    private IProducerProcessor knowledgeChangeMqProducer;

    @Override
    public void notifyWikiChange(WikiEvent wikiEvent) {
        try {
            ProducerResult result = knowledgeChangeMqProducer.sendMessage(JsonUtil.toJson(wikiEvent));
            ParamCheckUtil.isTrue(result != null && result.getProducerStatus().equals(ProducerStatus.SEND_OK)
                    , BizErrorEnum.SEND_MQ_ERROR);
            log.info("发送wiki变更事件成功, event: {}, result: {}", JsonUtil.toJson(wikiEvent), JsonUtil.toJson(result));
        } catch (Exception e) {
            log.error("发送wiki变更事件失败, event: {}, error: {}", JsonUtil.toJson(wikiEvent), e.getMessage(), e);
        }
    }

    @Override
    public void handlerWikiAdd(WikiEvent wikiEvent) {
        if (wikiEvent == null) {
            return;
        }

        String wikiEventStr = JsonUtil.toJson(wikiEvent);
        log.info("处理wiki新增事件开始, wikiEvent: {}", wikiEventStr);

        // 等待数据就绪（现在的 es 默认的数据刷新频率是 1 秒）
        sleepForDataReady();

        try {
            // 1. 参数校验
            validateWikiEvent(wikiEvent);

            // 2. 业务处理
            processWikiAdd(wikiEvent);

            log.info("处理wiki新增事件成功, wikiEvent: {}", wikiEventStr);
        } catch (Exception e) {
            log.error("处理wiki新增事件失败, wikiEvent: {}, error: {}", wikiEventStr, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void handlerWikiUpdate(WikiEvent wikiEvent) {
        if (wikiEvent == null) {
            return;
        }

        String wikiEventStr = JsonUtil.toJson(wikiEvent);
        log.info("处理wiki更新事件开始, wikiEvent: {}", wikiEventStr);

        // 等待数据就绪（现在的 es 默认的数据刷新频率是 1 秒）
        sleepForDataReady();

        try {
            // 参数校验
            validateWikiEvent(wikiEvent);

            // 业务处理
            processWikiUpdate(wikiEvent);

            log.info("处理wiki更新事件成功, wikiEvent: {}", wikiEventStr);
        } catch (Exception e) {
            log.error("处理wiki更新事件失败, wikiEvent: {}, error: {}", wikiEventStr, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void handlerWikiDelete(WikiEvent wikiEvent) {
        if (wikiEvent == null) {
            return;
        }

        String wikiEventStr = JsonUtil.toJson(wikiEvent);
        log.info("处理wiki删除事件开始, wikiEvent: {}", wikiEventStr);

        // 等待数据就绪（现在的 es 默认的数据刷新频率是 1 秒）
        sleepForDataReady();

        try {
            // 参数校验
            validateWikiEvent(wikiEvent);

            // 业务处理
            processWikiDelete(wikiEvent);

            log.info("处理wiki删除事件成功, wikiEvent: {}", wikiEventStr);
        } catch (Exception e) {
            log.error("处理wiki删除事件失败, wikiEvent: {}, error: {}", wikiEventStr, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 校验wiki事件参数
     */
    private void validateWikiEvent(WikiEvent wikiEvent) {
        // 校验WikiEvent不能为空
        ParamCheckUtil.notNull(wikiEvent, "WikiEvent不能为空");

        // 校验WikiId不能为空
        ParamCheckUtil.notBlank(wikiEvent.getType(), "WikiId不能为空");

        // 校验WikiId不能为空
        ParamCheckUtil.notNull(wikiEvent.getWikiId(), "WikiId不能为空");

        // 校验datasetId不能为空
        ParamCheckUtil.notNull(wikiEvent.getDatasetId(), "DatasetId不能为空");
    }

    /**
     * 处理wiki新增业务逻辑
     */
    private void processWikiAdd(WikiEvent wikiEvent) {
        List<WikiModel> wikiList = wikiRepository.findByQuery(WikiQuery.builder().datasetId(wikiEvent.getDatasetId())
                .wikiId(wikiEvent.getWikiId()).state(WikiStateEnum.ENABLE).build());
        ParamCheckUtil.isTrue(CollectionUtils.size(wikiList) == 1, BizErrorEnum.WIKI_STATE_ERROR);

        List<FragmentCoreModel> dataList = DefaultUtil.defaultList(fetchFragment(wikiEvent.getDatasetId()
                        , wikiEvent.getWikiId()))
                .stream()
                .map(FragmentCoreModel::from)
                .collect(Collectors.toList());

        fragmentCoreRepository.batchUpsert(wikiEvent.getDatasetId(), dataList);
    }

    /**
     * 处理wiki更新业务逻辑
     */
    private void processWikiUpdate(WikiEvent wikiEvent) {
        Long datasetId = wikiEvent.getDatasetId();
        Long wikiId = wikiEvent.getWikiId();

        List<WikiModel> wikiList = wikiRepository.findByQuery(WikiQuery.builder().datasetId(wikiEvent.getDatasetId())
                .wikiId(wikiEvent.getWikiId()).state(WikiStateEnum.ENABLE).build());
        ParamCheckUtil.isTrue(CollectionUtils.size(wikiList) == 1, BizErrorEnum.WIKI_STATE_ERROR);

        List<FragmentModel> fragmentModels = fetchFragment(datasetId, wikiId);
        List<FragmentCoreModel> fragmentCoreModels = fetchFragmentCore(datasetId, wikiId);

        // 将fragmentModels转换为Map，便于查找
        Map<String, FragmentModel> fragmentModelMap = DefaultUtil.defaultList(fragmentModels).stream()
                .collect(Collectors.toMap(FragmentModel::getId, Function.identity(),
                        (existing, replacement) -> existing));

        // 将fragmentCoreModels转换为Map，便于查找
        Map<String, FragmentCoreModel> fragmentCoreModelMap = DefaultUtil.defaultList(fragmentCoreModels).stream()
                .collect(Collectors.toMap(FragmentCoreModel::getId, Function.identity(),
                        (existing, replacement) -> existing));

        // 需要删除的数据：在fragmentCoreModels中存在但在fragmentModels中不存在
        List<String> idsToDelete = fragmentCoreModelMap.keySet().stream()
                .filter(id -> !fragmentModelMap.containsKey(id))
                .collect(Collectors.toList());

        // 需要新增或更新的数据
        List<FragmentCoreModel> dataToUpsert = new ArrayList<>();

        // 遍历最新的fragmentModels
        for (FragmentModel fragmentModel : DefaultUtil.defaultList(fragmentModels)) {
            String id = fragmentModel.getId();
            FragmentCoreModel newCoreModel = FragmentCoreModel.from(fragmentModel);

            // 如果在ES中不存在，则为新增
            if (!fragmentCoreModelMap.containsKey(id)) {
                dataToUpsert.add(newCoreModel);
                continue;
            }

            // 如果在ES中存在，比较是否需要更新
            FragmentCoreModel existingCoreModel = fragmentCoreModelMap.get(id);
            if (!Objects.equals(existingCoreModel, newCoreModel)) {
                dataToUpsert.add(newCoreModel);
            }
        }

        fragmentCoreRepository.batchUpsert(datasetId, dataToUpsert);
        fragmentCoreRepository.batchDelete(datasetId, idsToDelete);
    }

    /**
     * 处理wiki删除业务逻辑
     */
    private void processWikiDelete(WikiEvent wikiEvent) {
        List<WikiModel> wikiList = wikiRepository.findByQuery(WikiQuery.builder().datasetId(wikiEvent.getDatasetId())
                .wikiId(wikiEvent.getWikiId()).state(WikiStateEnum.ENABLE).build());
        ParamCheckUtil.isTrue(CollectionUtils.size(wikiList) == 0, BizErrorEnum.WIKI_STATE_ERROR);

        List<String> ids = DefaultUtil.defaultList(fetchFragmentCore(wikiEvent.getDatasetId(), wikiEvent.getWikiId()))
                .stream().map(FragmentCoreModel::getId).collect(Collectors.toList());
        fragmentCoreRepository.batchDelete(wikiEvent.getDatasetId(), ids);
    }

    private List<FragmentModel> fetchFragment(Long datasetId, Long wikiId) {
        return fragmentRepository.findAll(FragmentQuery.builder().datasetId(datasetId).wikiId(wikiId)
                .state(FragmentStateEnum.ENABLE).build());
    }

    private List<FragmentCoreModel> fetchFragmentCore(Long datasetId, Long wikiId) {
        return fragmentCoreRepository.findByQuery(FragmentCoreQuery.builder().datasetId(datasetId).wikiId(wikiId).build());
    }

    private void sleepForDataReady() {
        if (esDataReadInternalSecond > 0) {
            try {
                TimeUnit.SECONDS.sleep(esDataReadInternalSecond);
            } catch (InterruptedException e) {
                log.error("等待ES数据就绪时线程被中断, error: {}", e.getMessage(), e);
            }
        }
    }
}