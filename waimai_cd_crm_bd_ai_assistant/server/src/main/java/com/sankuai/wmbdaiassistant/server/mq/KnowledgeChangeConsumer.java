package com.sankuai.wmbdaiassistant.server.mq;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.server.application.service.dataset.KnowledgeChangeService;
import com.sankuai.wmbdaiassistant.server.mq.event.KnowledgeChangeEvent;
import com.sankuai.wmbdaiassistant.server.mq.event.WikiEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 监听知识变更消息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-23 14:39
 */
@Slf4j
@Component("knowledgeChangeConsumer")
public class KnowledgeChangeConsumer {

    @Resource
    private KnowledgeChangeService knowledgeChangeService;

    @MdpConfig("knowledge.change.consumer.switch:false")
    private Boolean knowledgeChangeConsumerSwitch;

    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String message) {
        log.info("接收到知识变更消息, message:{}", message);

        if (!DefaultUtil.defaultBoolean(knowledgeChangeConsumerSwitch)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        KnowledgeChangeEvent knowledgeChangeEvent = JsonUtil.fromJson(message, KnowledgeChangeEvent.class);
        if (knowledgeChangeEvent == null) {
            log.error("解析知识变更消息失败, message:{}", message);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        KnowledgeChangeType knowledgeChangeType = KnowledgeChangeType.getByCode(knowledgeChangeEvent.getType());
        if (knowledgeChangeType == null) {
            log.error("未知的知识变更类型, message:{}", message);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        ExecutorUtil.safeExecute(() -> {
            switch (knowledgeChangeType) {
                case WIKI_ADD: {
                    WikiEvent wikiAddEvent = JsonUtil.fromJson(message, WikiEvent.class);
                    knowledgeChangeService.handlerWikiAdd(wikiAddEvent);
                    break;
                }
                case WIKI_UPDATE: {
                    WikiEvent wikiUpdateEvent = JsonUtil.fromJson(message, WikiEvent.class);
                    knowledgeChangeService.handlerWikiUpdate(wikiUpdateEvent);
                    break;
                }
                case WIKI_DELETE: {
                    WikiEvent wikiDeleteEvent = JsonUtil.fromJson(message, WikiEvent.class);
                    knowledgeChangeService.handlerWikiDelete(wikiDeleteEvent);
                    break;
                }
                default:
                    break;
            }
        });
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
