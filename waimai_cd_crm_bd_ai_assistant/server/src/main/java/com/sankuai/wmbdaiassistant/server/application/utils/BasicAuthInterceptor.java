package com.sankuai.wmbdaiassistant.server.application.utils;

import java.io.IOException;
import okhttp3.Credentials;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 基础认证拦截器，用于在HTTP请求头中添加Basic Auth认证信息
 * 适用于需要身份验证的HTTP请求场景
 *
 * <AUTHOR>
 */
public class BasicAuthInterceptor implements Interceptor {

    // HTTP请求头中用于存放认证信息的字段名
    private static final String BASIC_AUTH_HEADER = "Authorization";

    // Basic Auth认证的凭据，格式为"Basic base64(user:password)"
    private final String credentials;

    /**
     * 构造方法，初始化认证凭据
     *
     * @param user 用户名
     * @param password 密码
     */
    public BasicAuthInterceptor(String user, String password) {
        // 使用OkHttp提供的工具类生成Basic Auth认证信息
        this.credentials = Credentials.basic(user, password);
    }

    /**
     * 拦截器方法，在请求发送前添加认证信息
     *
     * @param chain 拦截器链，用于处理请求和响应
     * @return 带有认证信息的响应
     * @throws IOException 如果请求处理过程中发生IO异常
     */
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Request authenticatedRequest = request.newBuilder()
                .header(BASIC_AUTH_HEADER, credentials).build();
        return chain.proceed(authenticatedRequest);
    }
}
