package com.sankuai.wmbdaiassistant.server.application.service.helpbd.impl;


import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.RobotType;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.EntryTrackingBo;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.TrackingManager;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.TrackingService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 埋点
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-02 10:22
 */
@Service
public class TrackingServiceImpl implements TrackingService {

    @Resource
    private TrackingManager trackingManager;

    @Override
    public void entryTracking(EntryTrackingBo entryTrackingBo) {
        if (entryTrackingBo == null) {
            return;
        }

        ParamCheckUtil.notNull(entryTrackingBo.getSource(), "入口埋点，来源不能为空");
        ParamCheckUtil.isTrue(RobotType.isValid(entryTrackingBo.getRobotType()), "机器人类型错误");
        ParamCheckUtil.notBlank(entryTrackingBo.getRobotId(), "入口埋点，机器人不能为空");

        trackingManager.entryTracking(entryTrackingBo);
    }

}
