package com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerAuthException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcTimeoutException;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtOnesRelationBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtTicketBo;
import com.sankuai.wmbdaiassistant.server.application.dto.PersonTtCountInfo;
import com.sankuai.wmbdaiassistant.server.application.dto.TtApiBaseDto;
import com.sankuai.wmbdaiassistant.server.application.dto.TtOnesRelationDto;
import com.sankuai.wmbdaiassistant.server.application.dto.TtTicketDto;
import com.sankuai.wmbdaiassistant.server.application.utils.BasicAuthInterceptor;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TtWrapper {

    private static final int CONNECT_TIMEOUT = 10;
    private static final int READ_TIMEOUT = 15;
    private static final String TT_SECRET = "skysea123";

    private static final String QUERY_TICKET_URL = "/api/1.0/ticket/{}";
    private static final String TICKET_INFO_URL = "/ticket/list?filter=createdBy&id={}";
    private static final String PERSONAL_INFO_URI = "/api/1.0/ticket/number/map";
    private static final String TT_ONES_RELATION_URI = "/api/1.0/associate/search?ticketId={}";

    private static final String HEADER_USER_NAME = "USERNAME";

    /**
     * 权限不足（一般是不是蜜蜂rg私有工单请求详情导致的
     */
    private static final int AUTH_FORBIDDEN = 400;

    public static final String APP_KEY = "com.sankuai.waimaimka.kaservice.skysea";

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            // Basic Auth 校验
            .addInterceptor(new BasicAuthInterceptor(APP_KEY, TT_SECRET))
            .build();

    public JsonNode queryTicket(Integer ticketId) {
        // 查询 tt 基本信息
        String url = MessageFormatter.format(HelpBDConfig.TT_API_HOST + QUERY_TICKET_URL, ticketId).getMessage();
        String ttTicketData = get(url, getSuperUserHeader(), new TypeReference<TtApiBaseDto<String>>() {
        });

        JsonNode jsonNode = JsonUtil.fromJson(ttTicketData);
        return jsonNode;
    }

    public TtTicketBo getTicket(Integer ticketId) {
        // 查询 tt 基本信息
        String url = MessageFormatter.format(HelpBDConfig.TT_API_HOST + QUERY_TICKET_URL, ticketId).getMessage();
        TtTicketDto ttTicketDto = get(url, getSuperUserHeader(), new TypeReference<TtApiBaseDto<TtTicketDto>>() {
        });

        TtTicketBo ttTicketBo = EnBeanUtils.copy(ttTicketDto, TtTicketBo::new);

        // 查询 tt 关联的 one
        try {
            TtOnesRelationBo ttOnesRelation = getTtOnesRelation(ticketId);
            if (Objects.nonNull(ttOnesRelation)) {
                ttTicketBo.setOnesId(ttOnesRelation.getOnesId());
            }
        } catch (RpcServerException e) {
            // 这个地方可以忽略，因为有补偿任务
            log.warn("查询 tt 关联 onesId 失败", e);
        }

        return ttTicketBo;
    }

    public TtOnesRelationBo getTtOnesRelation(Integer ticketId) {
        String url = MessageFormatter.format(HelpBDConfig.TT_API_HOST + TT_ONES_RELATION_URI, ticketId).getMessage();
        TtOnesRelationDto.TtOnesRelationWrapperDto wrapperDto = get(url, getSuperUserHeader(), new TypeReference<TtApiBaseDto<TtOnesRelationDto.TtOnesRelationWrapperDto>>() {
        });

        if (Objects.isNull(wrapperDto)) {
            return null;
        }
        List<TtOnesRelationDto> items = wrapperDto.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }

        return items.stream()
                .filter(TtOnesRelationDto::isOnesType)
                .map(dto -> EnBeanUtils.copy(dto, TtOnesRelationBo::new))
                .findAny()
                .orElse(null);
    }

    public String getTicketInfoUrl(Integer ticketId) {
        return MessageFormatter.format(HelpBDConfig.TT_WEB_HOST + TICKET_INFO_URL, ticketId).getMessage();
    }

    /**
     * 获取用户个人信息接口
     *
     * @param misId 用户标识
     * @return 各类工单数据
     */
    public PersonTtCountInfo getPersonalInfo(String misId) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_USER_NAME, misId);
        String url = HelpBDConfig.TT_API_HOST + PERSONAL_INFO_URI;
        return get(url, headers, new TypeReference<TtApiBaseDto<PersonTtCountInfo>>() {
        });
    }



    private <T> T get(String url, Map<String, String> headerMap, TypeReference<TtApiBaseDto<T>> type) {
        Request.Builder builder = new Request.Builder();

        if (Objects.nonNull(headerMap)) {
            for (Map.Entry<String, String> entry: headerMap.entrySet()) {
                String key = entry.getKey();
                builder.addHeader(key, headerMap.get(key));
            }
        }
        Request request = builder.url(url).build();
        return executeCall(request, type, url);
    }

    private <T> T executeCall(Request request, TypeReference<TtApiBaseDto<T>> type, String url) {
        try (Response resp = client.newCall(request).execute()) {
            if (!resp.isSuccessful()) {
                // 权限不足（一般是不是蜜蜂rg私有工单请求详情导致的
                if (resp.code() == AUTH_FORBIDDEN) {
                    throw new RpcServerAuthException(StringUtil.of("TT api auth forbidden. http message:{}", resp.message()));
                }
                throw new RpcServerException(StringUtil.of("TT api http code: {}, url is {}", resp.code(), url));
            }

            String body = null;
            if (resp.body() != null) {
                body = resp.body().string();
            }
            TtApiBaseDto<T> baseDto = JSON.parseObject(body, type);
            if (baseDto != null && baseDto.isFailure()) {
                // 权限不足（一般是不是蜜蜂rg私有工单请求详情导致的
                if (baseDto.isAuthForbidden()) {
                    throw new RpcServerAuthException(StringUtil.of("TT api auth forbidden. body:{}", body));
                }

                throw new RpcServerException(StringUtil.of("TT api error ,body is {}, url is {}", body, url));
            }
            if (baseDto != null) {
                return baseDto.getData();
            }
            return null;
        } catch (IOException e) {
            throw new RpcTimeoutException(StringUtil.of("TT api error, url is {}", url), e);
        }
    }

    private Map<String, String> getSuperUserHeader() {
        HashMap<String, String> header = Maps.newHashMap();
        header.put(HEADER_USER_NAME, HelpBDConfig.TT_SUPER_USER);
        return header;
    }

}
