package com.sankuai.wmbdaiassistant.server.application.service.dsl;

import com.sankuai.wmbdaiassistant.server.application.bo.HistoryGroupMsgsRequest;

import java.util.Map;

public interface DxMessageDslService {

    /**
     * 根据商家ID和会话ID获取商家所有驳回任务的修改建议（根据 session 内容的相关性排序）
     *
     * @param historyGroupMsgsRequest 请求
     * @return 搜索结果
     */
    Map<String, String> getHistoryGroupMsgs(HistoryGroupMsgsRequest historyGroupMsgsRequest);

}
