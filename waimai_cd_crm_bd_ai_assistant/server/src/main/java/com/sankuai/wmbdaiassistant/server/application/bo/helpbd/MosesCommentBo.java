package com.sankuai.wmbdaiassistant.server.application.bo.helpbd;

import com.sankuai.wmbdaiassistant.domain.enums.helpbd.MosesCommentTypeEnum;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 摩西服务评论
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-02-28 19:32
 */
@Data
public class MosesCommentBo {

    /**
     * 租户信息
     */
    private TenantBo tenant;

    /**
     * 评论ID
     */
    private Long id;

    /**
     * 机器人类型
     */
    private Integer robotType;

    /**
     * 摩西机器人ID/智能客服url
     */
    private String robotId;

    /**
     * 星级（1 ~ 5 ，分别代表1 ~ 5颗星）
     */
    @Deprecated
    private Integer star;

    /**
     * 值（与 desc 相匹配）
     */
    private Integer value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 评论
     */
    private String comment;

    /**
     * 快捷提示语
     */
    private List<String> tips;

    /**
     * 评论用户的 uid
     */
    private Long uid;

    /**
     * 评论用户的 misID
     */
    private String misId;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 评论的类型
     */
    private MosesCommentTypeEnum type;

}
