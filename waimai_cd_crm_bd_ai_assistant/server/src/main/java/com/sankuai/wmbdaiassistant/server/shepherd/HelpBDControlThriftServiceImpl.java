package com.sankuai.wmbdaiassistant.server.shepherd;

import cn.hutool.core.util.ObjectUtil;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeEntryTrackingDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeMosesCommentConfigRequestDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeMosesCommentRequestDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeUserDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.BeeMosesCommentStarConfigDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ExtResponse;
import com.sankuai.wmbdaiassistant.api.response.helpbd.PersonTtInfoDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftBaseDto;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ThriftCommonDto;
import com.sankuai.wmbdaiassistant.api.service.HelpBDControlThriftService;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.server.ThriftPortConstant;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.EntryTrackingBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.converter.HelpBDConverter;
import com.sankuai.wmbdaiassistant.server.application.dto.UserDto;
import com.sankuai.wmbdaiassistant.server.application.dto.MosesCommentStarConfigDto;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentConfigBo;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.CommentService;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.TrackingService;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.TtHelpbdService;
import com.sankuai.wmbdaiassistant.server.application.utils.ThriftInvokerWrapper;
import com.sankuai.wmbdaiassistant.server.filter.ChatSessionFilter;
import com.sankuai.wmbdaiassistant.server.filter.ExceptionHanderFilter;
import com.sankuai.wmbdaiassistant.server.filter.UserFilter;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 扩展接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 15:20
 */
@Slf4j
@MdpThriftServer(port = ThriftPortConstant.PORT_9011)
@MdpThriftServerExtConfig(filters = {UserFilter.class, ExceptionHanderFilter.class})
public class HelpBDControlThriftServiceImpl implements HelpBDControlThriftService {
    private static final String MESSAGE_USER_NO_INFO = "用户信息查询失败。";

    @Resource
    private TtHelpbdService ttHelpbdService;

    @Resource
    private TrackingService trackingService;

    @Resource
    private CommentService commentService;

    @Override
    public ExtResponse<PersonTtInfoDto> getTtInfo() {
        User user = UserUtils.getUser();
        if (ObjectUtil.isNull(user)) {
            throw new IllegalArgumentException("未获取到用户信息");
        }
        // 获取 TT 相关信息
        PersonTtInfoDto ttInfo = ttHelpbdService.getPersonalInfo(user.getId(), user.getLogin());
        if (ObjectUtil.isNull(ttInfo)) {
            throw new IllegalArgumentException(MESSAGE_USER_NO_INFO);
        }

        return ExtResponse.success(ttInfo);
    }

    @Override
    public ThriftBaseDto mosesComment(BeeUserDto beeUser, BeeMosesCommentRequestDto mosesCommentRequestDto) {

        return ThriftInvokerWrapper.handleWithoutResult(() -> {
            ParamCheckUtil.notNull(beeUser, "用户信息不能为空");
            ParamCheckUtil.notNull(mosesCommentRequestDto, "评论信息不能为空");

            if (ObjectUtil.isNull(beeUser.getUid()) || ObjectUtil.isNull(beeUser.getMisId())) {
                User user = UserUtils.getUser();
                if (ObjectUtil.isNotNull(user)) {
                    beeUser.setUid((long) user.getId());
                    beeUser.setMisId(user.getLogin());
                } else {
                    throw new IllegalArgumentException("评论时，未获取到用户信息");
                }
            }
            MosesCommentBo mosesCommentBo = HelpBDConverter.toBoWithMoses(beeUser, mosesCommentRequestDto);
            commentService.mosesComment(mosesCommentBo);
        });
    }

    @Override
    public ThriftCommonDto<List<BeeMosesCommentStarConfigDto>> fetchMosesCommentConfig(BeeUserDto beeUser,
        BeeMosesCommentConfigRequestDto requestDto) {
        return ThriftInvokerWrapper.handle(() -> {
            ParamCheckUtil.notNull(beeUser, "获取评论配置时，查询条件不能为空");

            if (ObjectUtil.isNull(beeUser.getUid()) || ObjectUtil.isNull(beeUser.getMisId())) {
                User user = UserUtils.getUser();
                if (ObjectUtil.isNotNull(user)) {
                    beeUser.setUid((long) user.getId());
                    beeUser.setMisId(user.getLogin());
                } else {
                    throw new IllegalArgumentException("获取评论配置时，未获取到用户信息");
                }
            }
            UserDto userDto = UserDto.of(beeUser);
            MosesCommentConfigBo mosesCommentConfigBo = EnBeanUtils.copy(requestDto, MosesCommentConfigBo::new);

            List<MosesCommentStarConfigDto> configList = commentService.getMosesStarCommentConfig(userDto,
                mosesCommentConfigBo);
            if (CollectionUtils.isEmpty(configList)) {
                log.info("【BD在线提问】mis = {} 无法评论，{}", beeUser.getMisId(), LocalDate.now());
                return Collections.emptyList();
            }
            log.info("【BD在线提问】mis = {} 今日看到了评论", beeUser.getMisId());
            return configList.stream()
                .map(config -> EnBeanUtils.copy(config, BeeMosesCommentStarConfigDto::new)).collect(Collectors.toList());
        });
    }

    @Override
    public void entryTracking(BeeUserDto beeUser, BeeEntryTrackingDto beeEntryTrackingDto) {
        ThriftInvokerWrapper.handleWithVoid(() -> {
            ParamCheckUtil.notNull(beeEntryTrackingDto, "埋点信息不能为空");
            ParamCheckUtil.notNull(beeUser, "用户信息不能为空");

            EntryTrackingBo entryTrackingBo;
            if (ObjectUtil.isNull(beeUser.getUid()) || ObjectUtil.isNull(beeUser.getMisId())) {
                User user = UserUtils.getUser();
                if (ObjectUtil.isNull(user)) {
                    throw new IllegalArgumentException("获取用户信息失败");
                }
                beeUser.setUid((long) user.getId());
                beeUser.setMisId(user.getLogin());
            }
            entryTrackingBo = HelpBDConverter.toBoWhenBeeUser(beeUser, beeEntryTrackingDto);
            trackingService.entryTracking(entryTrackingBo);
        });
    }
}
