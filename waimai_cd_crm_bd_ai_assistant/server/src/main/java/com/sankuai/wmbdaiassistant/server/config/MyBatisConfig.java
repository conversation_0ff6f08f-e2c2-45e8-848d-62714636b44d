package com.sankuai.wmbdaiassistant.server.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

/**
 * mybatis配置，解决多SqlSessionFactory
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/18 17:28
 */
@Configuration
public class MyBatisConfig {

    @Resource(name = "sqlSessionFactory0")
    private SqlSessionFactory bdaiassistantSqlSessionFactory;

    @Resource(name = "sqlSessionFactory1")
    private SqlSessionFactory skyseaSqlSessionFactory;

    @Bean
    @Primary
    public SqlSessionFactory defaultSqlSessionFactory() {
        // 设置一个默认的SqlSessionFactory，通常是主要使用的那个
        return bdaiassistantSqlSessionFactory;
    }

    @Bean
    public SqlSessionFactory getBdaiassistantSqlSessionFactory() {
        return bdaiassistantSqlSessionFactory;
    }

    @Bean
    public SqlSessionFactory getSkyseaSqlSessionFactory() {
        return skyseaSqlSessionFactory;
    }
}
