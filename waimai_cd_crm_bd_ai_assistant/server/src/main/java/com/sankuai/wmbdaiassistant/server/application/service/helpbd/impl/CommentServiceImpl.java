package com.sankuai.wmbdaiassistant.server.application.service.helpbd.impl;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.RobotType;
import com.sankuai.wmbdaiassistant.server.application.dto.MosesCommentStarConfigDto;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.CommentTimesMccBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.dto.UserDto;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentConfigBo;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.MosesCommentManager;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.CommentService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 评论服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-01 11:18
 */
@Service
public class CommentServiceImpl implements CommentService {

    @Resource
    private MosesCommentManager mosesCommentManager;

    @Override
    public void mosesComment(MosesCommentBo mosesCommentBo) {
        ParamCheckUtil.notNull(mosesCommentBo, "评论不能为空");
        ParamCheckUtil.notNull(mosesCommentBo.getType(), "评论类型不能为空");
        ParamCheckUtil.isTrue(RobotType.isValid(mosesCommentBo.getRobotType()), "机器人类型错误");
        ParamCheckUtil.notBlank(mosesCommentBo.getRobotId(), "摩西机器人ID不能为空");

        mosesCommentManager.comment(mosesCommentBo);
    }

    @Override
    public List<MosesCommentStarConfigDto> getMosesStarCommentConfig(@NonNull UserDto user, MosesCommentConfigBo mosesCommentConfigBo) {
        if (isUserCommentLimited(user, mosesCommentConfigBo)) {
            return Collections.emptyList();
        }
        return getUserMosesComment(mosesCommentConfigBo.getNewConfig());
    }

    private boolean isUserCommentLimited(UserDto user, MosesCommentConfigBo mosesCommentConfigBo) {

        String key = generateKey(mosesCommentConfigBo);
        if (CollectionUtils.isNotEmpty(HelpBDConfig.MOSES_COMMENT_FORBIDDEN_LIST) && HelpBDConfig.MOSES_COMMENT_FORBIDDEN_LIST.contains(key)) {
            return true;
        }

        List<MosesCommentBo> mosesCommentBos = mosesCommentManager.getUserTodayComments(user);
        return CollectionUtils.size(mosesCommentBos) >= getMosesCommentMaxTimes(key);
    }

    private List<MosesCommentStarConfigDto> getUserMosesComment(Boolean newConfig) {
        if (newConfig == null || !newConfig) {
            ParamCheckUtil.notEmpty(HelpBDConfig.MOSES_COMMENT_CONFIG, "评论配置不能为空");
            return HelpBDConfig.MOSES_COMMENT_CONFIG.stream()
                    .map(s -> EnBeanUtils.copy(s, MosesCommentStarConfigDto::new)).collect(Collectors.toList());
        }

        ParamCheckUtil.notNull(HelpBDConfig.MOSES_COMMENT_CONFIG_NEW, "评论配置不能为空");
        return HelpBDConfig.MOSES_COMMENT_CONFIG_NEW.stream()
                .map(s -> EnBeanUtils.copy(s, MosesCommentStarConfigDto::new)).collect(Collectors.toList());
    }

    private String generateKey(MosesCommentConfigBo mosesCommentConfigBo) {
        if (mosesCommentConfigBo == null
                || mosesCommentConfigBo.getTenantId() == null
                || mosesCommentConfigBo.getBizId() == null) {
            return StringUtils.EMPTY;
        }
        return String.format("%s-%s", mosesCommentConfigBo.getTenantId(), mosesCommentConfigBo.getBizId());
    }

    private int getMosesCommentMaxTimes(String key) {
        ParamCheckUtil.notNull(HelpBDConfig.MOSES_COMMENT_TIMES_CONFIG, "评论限频次配置为空");

        int defaultMaxTimes = 0;
        for (CommentTimesMccBo commentTimesMccBo : HelpBDConfig.MOSES_COMMENT_TIMES_CONFIG) {
            if (CollectionUtils.isEmpty(commentTimesMccBo.getKeyList())) {
                defaultMaxTimes = commentTimesMccBo.getMaxTimes();
            }
            if (CollectionUtils.isNotEmpty(commentTimesMccBo.getKeyList()) && commentTimesMccBo.getKeyList().contains(key)) {
                return commentTimesMccBo.getMaxTimes();
            }
        }
        return defaultMaxTimes;
    }
}
