package com.sankuai.wmbdaiassistant.server.application.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TtOnesRelationDto {

    private static final String ONES_TYPE = "ONES";

    @JSONField(name = "source")
    private String ticketId;

    @JSONField(name = "destination")
    private String onesId;

    private String linkType;

    @Data
    public static class TtOnesRelationWrapperDto {
        private List<TtOnesRelationDto> items;
    }

    public boolean isOnesType() {
        return ONES_TYPE.equals(linkType);
    }
}
