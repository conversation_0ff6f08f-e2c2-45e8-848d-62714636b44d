package com.sankuai.wmbdaiassistant.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.JsonUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.FragmentModel;
import com.sankuai.wmbdaiassistant.domain.model.WikiModel;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentRepository;
import com.sankuai.wmbdaiassistant.domain.repository.WikiRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentQuery;
import com.sankuai.wmbdaiassistant.domain.repository.query.WikiQuery;
import com.sankuai.wmbdaiassistant.infrastructure.integration.redis.SquirrelClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@CraneConfiguration
public class EsDataDiffJob {

    /**
     * 对比进度缓存key
     */
    private final static String DIFF_PROCESS_KEY = "es_diff_process";

    @Resource
    private WikiRepository wikiRepository;

    @Resource
    private FragmentRepository fragmentRepository;

    @Resource
    private SquirrelClient squirrelClient;

    /**
     * scroll上下文超时时间，默认20分钟
     */
    @MdpConfig("esDataDiffJob.scroll.timeout:1200")
    private Long scrollTimeSeconds;

    /**
     * 应急开关，强制中断执行，避免对es集群有影响
     */
    @MdpConfig("esDataDiffJob.interrupt:false")
    private Boolean esDataDiffJobInterrupt;

    @Crane("auto-diff-es-data-job")
    public void diff() {
        log.info("EsDataDiffJob start");

        if (esDataDiffJobInterrupt) {
            log.info("EsDataDiffJob interrupt");
            return;
        }

        Map<String, String> diffProcess = squirrelClient.get(DIFF_PROCESS_KEY);
        if (diffProcess == null) {
            diffProcess = new HashMap<>();
            diffProcess.put("wikiOldToNewDiff", DateUtil.yesterday());
            diffProcess.put("wikiNewToOldDiff", DateUtil.yesterday());
            diffProcess.put("fragmentOldToNewDiff", DateUtil.yesterday());
            diffProcess.put("fragmentNewToOldDiff", DateUtil.yesterday());
        }
        String today = DateUtil.today();

        if(!StringUtils.equals(today, diffProcess.get("wikiOldToNewDiff"))){
            List<Map<String, WikiModel>> wikiOldToNewDiff = diffWiki("old", "new");
            diffProcess.put("wikiOldToNewDiff", today);

            log.info("EsDataDiffJob result wikiOldToNewDiff_detail, processDate:{}, diffNum:{},diffRetMap:{}", today, wikiOldToNewDiff.size(), JsonUtil.toJson(wikiOldToNewDiff));

            List<String> diffIds = wikiOldToNewDiff.stream().map(m -> m.get("src").getId()).collect(Collectors.toList());
            log.info("EsDataDiffJob result wikiOldToNewDiff_digest, processDate:{}, diffNum:{},diffIds:{}", today, wikiOldToNewDiff.size(), JsonUtil.toJson(diffIds));

            squirrelClient.set(DIFF_PROCESS_KEY, diffProcess);
        }

        if (!StringUtils.equals(today, diffProcess.get("wikiNewToOldDiff"))) {
            List<Map<String, WikiModel>> wikiNewToOldDiff = diffWiki("new", "old");
            diffProcess.put("wikiNewToOldDiff", today);

            log.info("EsDataDiffJob result wikiNewToOldDiff_detail, processDate:{}, diffNum:{},diffRetMap:{}", today, wikiNewToOldDiff.size(), JsonUtil.toJson(wikiNewToOldDiff));
            List<String> diffIds = wikiNewToOldDiff.stream().map(m -> m.get("src").getId()).collect(Collectors.toList());
            log.info("EsDataDiffJob result wikiNewToOldDiff_digest, processDate:{}, diffNum:{},diffIds:{}", today, wikiNewToOldDiff.size(), JsonUtil.toJson(diffIds));
            squirrelClient.set(DIFF_PROCESS_KEY, diffProcess);
        }


        if (!StringUtils.equals(today, diffProcess.get("fragmentOldToNewDiff"))) {
            List<Map<String, FragmentModel>> fragmentOldToNewDiff = diffFragment("old", "new");
            diffProcess.put("fragmentOldToNewDiff", today);

            log.info("EsDataDiffJob result fragmentOldToNewDiff_detail, processDate:{}, diffNum:{},diffRetMap:{}", today, fragmentOldToNewDiff.size(), JsonUtil.toJson(fragmentOldToNewDiff));
            List<String> diffIds = fragmentOldToNewDiff.stream().map(m -> m.get("src").getId()).collect(Collectors.toList());
            log.info("EsDataDiffJob result fragmentOldToNewDiff_digest, processDate:{}, diffNum:{},diffIds:{}", today, fragmentOldToNewDiff.size(), JsonUtil.toJson(diffIds));

            squirrelClient.set(DIFF_PROCESS_KEY, diffProcess);
        }

        if (!StringUtils.equals(today, diffProcess.get("fragmentNewToOldDiff"))) {
            List<Map<String, FragmentModel>> fragmentNewToOldDiff = diffFragment("new", "old");
            diffProcess.put("fragmentNewToOldDiff", today);

            log.info("EsDataDiffJob result fragmentNewToOldDiff_detail, processDate:{}, diffNum:{},diffRetMap:{}", today, fragmentNewToOldDiff.size(), JsonUtil.toJson(fragmentNewToOldDiff));
            List<String> diffIds = fragmentNewToOldDiff.stream().map(m -> m.get("src").getId()).collect(Collectors.toList());
            log.info("EsDataDiffJob result fragmentNewToOldDiff_digest, processDate:{}, diffNum:{},diffIds:{}", today, fragmentNewToOldDiff.size(), JsonUtil.toJson(diffIds));
            squirrelClient.set(DIFF_PROCESS_KEY, diffProcess);
        }

        log.info("EsDataDiffJob end");
    }

    /**
     * 对比wiki_index数据
     * 
     * @param sourceMode
     * @param dstMode
     * @return
     */
    private List<Map<String, WikiModel>> diffWiki(String sourceMode, String dstMode) {
        WikiQuery wikiQuery = new WikiQuery();
        wikiQuery.setPageSize(1000);
        wikiQuery.setStartTime(String.valueOf(DateUtil.parseDate("2025-01-01 00:00:00").getTime()));

        List<Map<String, WikiModel>> diffObjList = new ArrayList<>();

        wikiRepository.scrollQuery(wikiQuery, dataList -> {

            ParamCheckUtil.isTrue(!esDataDiffJobInterrupt, "esDataDiffJob interrupt");

            Map<String, WikiModel> srcWikiModelMap = dataList.stream()
                    .collect(Collectors.toMap(WikiModel::getId, m -> m, (exist, replace) -> exist));

            WikiQuery dstWikiQuery = new WikiQuery();
            dstWikiQuery.setIdList(new ArrayList<>(srcWikiModelMap.keySet()));
            List<WikiModel> dstWikiModelList = wikiRepository.findAll(dstWikiQuery, dstMode);

            Map<String, WikiModel> dstWikiModelMap = dstWikiModelList.stream()
                    .collect(Collectors.toMap(WikiModel::getId, m -> m, (exist, replace) -> exist));

            for (String id : srcWikiModelMap.keySet()) {
                WikiModel srcWikiModel = srcWikiModelMap.get(id);
                WikiModel dstWikiModel = dstWikiModelMap.get(id);
                if (!WikiModel.compare(srcWikiModel, dstWikiModel)) {
                    Map<String, WikiModel> diffObj = new HashMap<>();
                    diffObj.put("src", srcWikiModel);
                    diffObj.put("dst", dstWikiModel);
                    diffObjList.add(diffObj);
                }
            }
        }, sourceMode, scrollTimeSeconds);

        return diffObjList;
    }

    /**
     * 对比fragment_index
     * 
     * @param sourceMode
     * @param dstMode
     * @return
     */
    private List<Map<String, FragmentModel>> diffFragment(String sourceMode, String dstMode) {
        FragmentQuery fragmentQuery = new FragmentQuery();
        fragmentQuery.setPageSize(1000);
        fragmentQuery.setUpdateStartTime(DateUtil.parseDate("2025-01-01 00:00:00"));

        List<Map<String, FragmentModel>> diffObjList = new ArrayList<>();

        fragmentRepository.scrollQuery(fragmentQuery, dataList -> {

            ParamCheckUtil.isTrue(!esDataDiffJobInterrupt, "esDataDiffJob interrupt");

            Map<String, FragmentModel> sourceFragmentModelMap = dataList.stream()
                    .collect(Collectors.toMap(FragmentModel::getId, m -> m, (exist, replace) -> exist));

            FragmentQuery dstFragmentQuery = new FragmentQuery();
            dstFragmentQuery.setIds(new ArrayList<>(sourceFragmentModelMap.keySet()));
            List<FragmentModel> dstFragmentModelList = fragmentRepository.findAll(dstFragmentQuery, dstMode);

            Map<String, FragmentModel> dstFragmentModelMap = dstFragmentModelList.stream()
                    .collect(Collectors.toMap(FragmentModel::getId, m -> m, (exist, replace) -> exist));

            for (String id : sourceFragmentModelMap.keySet()) {
                FragmentModel sourceFragmentModel = sourceFragmentModelMap.get(id);
                FragmentModel dstFragmentModel = dstFragmentModelMap.get(id);

                if (!FragmentModel.compare(sourceFragmentModel, dstFragmentModel)) {
                    Map<String, FragmentModel> diffObj = new HashMap<>();
                    diffObj.put("src", sourceFragmentModel);
                    diffObj.put("dst", dstFragmentModel);
                    diffObjList.add(diffObj);
                }
            }
        }, sourceMode, scrollTimeSeconds);

        return diffObjList;
    }
}
