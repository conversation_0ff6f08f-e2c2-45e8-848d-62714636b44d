package com.sankuai.wmbdaiassistant.server.application.dto;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OnesInfoDto {

    private static final String STATE_NAME_PATH = "$.description";

    private static final List<String> JSON_PREFIX = Lists.newArrayList("{", "[");

    @JSONField(name = "id")
    private String onesId;

    private String state;

    public void setState(String state) {
        // 如果是 json ，则获取 json 里面的 description
        if (Objects.nonNull(state) && JSON_PREFIX.stream().anyMatch(state::startsWith)) {
            this.state = JSONPath.eval(state, STATE_NAME_PATH).toString();
            return;
        }

        this.state = state;
    }
}
