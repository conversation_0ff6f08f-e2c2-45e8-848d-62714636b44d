package com.sankuai.wmbdaiassistant.server.mq.event;

import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.server.mq.KnowledgeChangeType;
import lombok.Getter;
import lombok.Setter;

/**
 * wiki 事件
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-23 10:31
 */
@Getter
@Setter
public class WikiEvent extends KnowledgeChangeEvent {

    /**
     * wiki id
     */
    private Long wikiId;

    /**
     * 数据集id
     */
    private Long datasetId;

    public WikiEvent() {
    }

    public WikiEvent(KnowledgeChangeType type, Long wikiId, Long datasetId) {
        ParamCheckUtil.notNull(type, "知识变更类型不能为空");
        this.setType(type.getCode());
        this.wikiId = wikiId;
        this.datasetId = datasetId;
    }
}
