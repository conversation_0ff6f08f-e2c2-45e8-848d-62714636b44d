package com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.impl;

import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.MosesCommentTypeEnum;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.MosesCommentPo;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.example.MosesCommentPoExample;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.MosesCommentMapper;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TenantBo;
import com.sankuai.wmbdaiassistant.server.application.dto.UserDto;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.MosesCommentManager;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 摩西评论
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-03-01 14:11
 */
@Component
public class MosesCommentManageImpl implements MosesCommentManager {

    @Resource
    private MosesCommentMapper mosesCommentMapper;


    @Override
    public void comment(MosesCommentBo mosesCommentBo) {

        ParamCheckUtil.notNull(mosesCommentBo, "评论信息不能为空");

        mosesCommentMapper.insert(toPo(mosesCommentBo));
    }

    @Override
    public List<MosesCommentBo> getUserTodayComments(UserDto userDto) {
        ParamCheckUtil.notNull(userDto, "用户信息不能为空");

        MosesCommentPoExample example = new MosesCommentPoExample();
        MosesCommentPoExample.Criteria criteria = example.createCriteria();

        criteria.andMisIdEqualTo(userDto.getMisId());
        criteria.andUidEqualTo(userDto.getUid());
        criteria.andCtimeGreaterThanOrEqualTo(DateUtil.getTodayFirstSecond());
        criteria.andCtimeLessThanOrEqualTo(DateUtil.getTodayLastSecond());

        List<MosesCommentPo> mosesCommentPos = mosesCommentMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mosesCommentPos)) {
            return Collections.emptyList();
        }

        return mosesCommentPos.stream().map(this::toBo).collect(Collectors.toList());
    }

    private MosesCommentPo toPo(MosesCommentBo mosesCommentBo) {
        if (mosesCommentBo == null) {
            return null;
        }
        MosesCommentPo mosesCommentPo = new MosesCommentPo();

        mosesCommentPo.setId(mosesCommentBo.getId());
        mosesCommentPo.setMisId(mosesCommentBo.getMisId());
        mosesCommentPo.setUid(mosesCommentBo.getUid());
        mosesCommentPo.setType(mosesCommentBo.getType().getCode());
        if (mosesCommentBo.getTenant() != null) {
            mosesCommentPo.setTenantId(mosesCommentBo.getTenant().getTenantId());
            mosesCommentPo.setTenantName(mosesCommentBo.getTenant().getTenantName());
            mosesCommentPo.setBizId(mosesCommentBo.getTenant().getBizId());
            mosesCommentPo.setBizName(mosesCommentBo.getTenant().getBizName());
        }
        mosesCommentPo.setRobotType(mosesCommentBo.getRobotType());
        mosesCommentPo.setRobotId(mosesCommentBo.getRobotId());
        mosesCommentPo.setStar(mosesCommentBo.getStar());
        mosesCommentPo.setValue(mosesCommentBo.getValue());
        mosesCommentPo.setDesc(mosesCommentBo.getDesc());
        if (CollectionUtils.isNotEmpty(mosesCommentBo.getTips())) {
            mosesCommentPo.setTips(StringUtils.join(mosesCommentBo.getTips(), ","));
        }
        mosesCommentPo.setComment(mosesCommentBo.getComment());
        mosesCommentPo.setCtime(mosesCommentBo.getCreatedTime());

        return mosesCommentPo;
    }

    private MosesCommentBo toBo(MosesCommentPo mosesCommentPo) {
        if (mosesCommentPo == null) {
            return null;
        }
        MosesCommentBo mosesCommentBo = new MosesCommentBo();
        mosesCommentBo.setId(mosesCommentPo.getId());
        mosesCommentBo.setRobotId(mosesCommentPo.getRobotId());
        mosesCommentBo.setStar(mosesCommentPo.getStar());
        mosesCommentBo.setDesc(mosesCommentPo.getDesc());
        mosesCommentBo.setComment(mosesCommentPo.getComment());
        if (StringUtils.isNotBlank(mosesCommentPo.getTips())) {
            mosesCommentBo.setTips(Arrays.asList(mosesCommentPo.getTips().split(",")));
        }
        mosesCommentBo.setUid(mosesCommentPo.getUid());
        mosesCommentBo.setMisId(mosesCommentPo.getMisId());
        mosesCommentBo.setCreatedTime(mosesCommentPo.getCtime());
        mosesCommentBo.setType(MosesCommentTypeEnum.getByCode(mosesCommentPo.getType()));

        TenantBo tenantBo = new TenantBo();
        tenantBo.setTenantId(mosesCommentPo.getTenantId());
        tenantBo.setTenantName(mosesCommentPo.getTenantName());
        tenantBo.setBizId(mosesCommentPo.getBizId());
        tenantBo.setBizName(mosesCommentPo.getBizName());
        mosesCommentBo.setTenant(tenantBo);

        return mosesCommentBo;
    }
}
