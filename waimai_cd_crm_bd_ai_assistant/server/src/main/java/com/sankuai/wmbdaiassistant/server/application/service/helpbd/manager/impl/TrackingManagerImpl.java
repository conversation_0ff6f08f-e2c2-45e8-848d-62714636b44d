package com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.impl;

import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.EntryTrackingPo;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.EntryTrackingMapper;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.EntryTrackingBo;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.TrackingManager;
import java.util.Date;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 埋点管理器
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 19:57
 */
@Component
public class TrackingManagerImpl implements TrackingManager {

    @Resource
    private EntryTrackingMapper entryTrackingMapper;

    @Override
    public void entryTracking(EntryTrackingBo entryTrackingBo) {
        entryTrackingMapper.insert(toPo(entryTrackingBo));
    }

    private EntryTrackingPo toPo(EntryTrackingBo entryTrackingBo) {
        EntryTrackingPo entryTrackingPo = new EntryTrackingPo();

        entryTrackingPo.setMisId(entryTrackingBo.getMisId());
        entryTrackingPo.setUid(entryTrackingBo.getUid());
        entryTrackingPo.setSource(entryTrackingBo.getSource() != null ? entryTrackingBo.getSource().getCode() : null);
        if (entryTrackingBo.getTenant() != null) {
            entryTrackingPo.setTenantId(entryTrackingBo.getTenant().getTenantId());
            entryTrackingPo.setTenantName(entryTrackingBo.getTenant().getTenantName());
            entryTrackingPo.setBizId(entryTrackingBo.getTenant().getBizId());
            entryTrackingPo.setBizName(entryTrackingBo.getTenant().getBizName());
        }
        entryTrackingPo.setRobotType(entryTrackingBo.getRobotType());
        entryTrackingPo.setRobotId(entryTrackingBo.getRobotId());
        entryTrackingPo.setCtime(new Date());

        return entryTrackingPo;
    }
}
