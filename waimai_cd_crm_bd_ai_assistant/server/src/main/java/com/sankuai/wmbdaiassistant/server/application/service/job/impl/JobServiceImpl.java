package com.sankuai.wmbdaiassistant.server.application.service.job.impl;

import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.wmbdaiassistant.api.response.JobItemResponseDto;
import com.sankuai.wmbdaiassistant.api.response.JobListResponseDto;
import com.sankuai.wmbdaiassistant.api.response.JobResponseDto;
import com.sankuai.wmbdaiassistant.api.response.RunningJobCountResponseDto;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.JobStatusEnum;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.JobItemModel;
import com.sankuai.wmbdaiassistant.domain.model.JobModel;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.repository.JobItemRepository;
import com.sankuai.wmbdaiassistant.domain.repository.JobRepository;
import com.sankuai.wmbdaiassistant.domain.repository.TraceLogRepository;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.infrastructure.integration.poi.PoiClient;
import com.sankuai.wmbdaiassistant.server.application.converter.JobConverter;
import com.sankuai.wmbdaiassistant.server.application.service.job.JobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务接口
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/07/08
 */
@Slf4j
@Service
public class JobServiceImpl implements JobService {

    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobItemRepository jobItemRepository;

    @Resource
    private PoiClient poiClient;

    @Resource
    private TraceLogService traceLogService;

    @Resource
    private TraceLogRepository traceLogRepository;

    @Override
    public JobListResponseDto getJobs(String mis, Long sessionId, String jobType, Date startDate, Date endDate) {
        ParamCheckUtil.notBlank(mis, "mis不能为空");
        ParamCheckUtil.notNull(sessionId, "sessionId不能为空");
        ParamCheckUtil.notBlank(jobType, "jobType不能为空");
        ParamCheckUtil.notNull(startDate, "startDate不能为空");
        ParamCheckUtil.notNull(endDate, "endDate不能为空");

        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildJobListTraceLog(sessionId, mis)));

        JobStatistics statistics = new JobStatistics();
        List<JobModel> jobModels = jobRepository.findByMisAndTypeAndDateRange(mis, jobType, startDate, endDate);
        if (CollectionUtils.isEmpty(jobModels)) {
            return buildJobListResponse(Collections.emptyList(), statistics);
        }

        List<JobResponseDto> jobList = getJobResponseDtos(jobModels, statistics);
        
        return buildJobListResponse(jobList, statistics);
    }

    @Override
    public RunningJobCountResponseDto getPoiDiagnosisRunningJobsNum(String mis, Date startDate, Date endDate) {
        ParamCheckUtil.notBlank(mis, "mis不能为空");
        ParamCheckUtil.notNull(startDate, "startDate不能为空");
        ParamCheckUtil.notNull(endDate, "endDate不能为空");

        List<JobModel> jobModels = jobRepository.findByMisAndTypeAndDateRange(
                                        mis, JobTypeEnum.POI_DIAGNOSIS.getCode(), startDate, endDate);
        if (CollectionUtils.isEmpty(jobModels)) { // 日期范围内 无job创建
            return buildRunningJobCountResponseDto(0L, false);
        }

        List<Long> jobIds = jobModels.stream().map(JobModel::getId).collect(Collectors.toList());
        Long runnings = jobItemRepository.countByJobIdsAndStatus(jobIds, JobStatusEnum.INIT.getCode());

        if (runnings == 0) { // 任务项全部完成
            // 用户最新一次看到任务全部完成弹窗的时间
            Date lastestJobViewedDate = traceLogRepository.findLatestDiagnosticJobViewedDate(mis);
            // 最新一条任务项完成的时间
            Date latestJobItemUpdatedDate = jobItemRepository.findLatestUpdatedDateByJobIds(jobIds);

            if (latestJobItemUpdatedDate == null ) {
                return buildRunningJobCountResponseDto(runnings, false);
            }

            if (lastestJobViewedDate == null) { // 用户从没查看过
                return buildRunningJobCountResponseDto(runnings, true);
            }

            Boolean needToClick = lastestJobViewedDate.before(latestJobItemUpdatedDate);
            return buildRunningJobCountResponseDto(runnings, needToClick);
        }

        return buildRunningJobCountResponseDto(runnings, false);
    }
            
    private List<JobResponseDto> getJobResponseDtos(List<JobModel> jobModels, JobStatistics statistics) {
        return jobModels.stream()
                .map(jobModel -> getJobResponseDto(jobModel, statistics))
                .collect(Collectors.toList());
    }

    private JobResponseDto getJobResponseDto(JobModel jobModel, JobStatistics statistics) {
        JobResponseDto jobResponseDto = new JobResponseDto();
        jobResponseDto.setCreateTime(jobModel.getCtime().getTime());
        jobResponseDto.setType(jobModel.getType() == null ? null : jobModel.getType().getCode());
            
        List<JobItemModel> jobItemModels = jobItemRepository.findByJobId(jobModel.getId());
        List<JobItemResponseDto> itemList = buildJobItemResponseDtos(jobItemModels, statistics);
        jobResponseDto.setItemList(itemList);

        return jobResponseDto;
    }
        
    private List<JobItemResponseDto> buildJobItemResponseDtos(List<JobItemModel> jobItemModels, JobStatistics statistics) {
        return jobItemModels.stream()
                .map(jobItemModel -> {
                    WmPoiAggre poiInfo = getPoiInfo(jobItemModel.getEntityId());
                    updateStatistics(jobItemModel.getStatus() != null ? jobItemModel.getStatus().getCode() : null,
                            statistics);
                    return JobConverter.toJobItemResponseDto(
                            jobItemModel,
                            poiInfo.getName(),
                            poiInfo.getPic_url()
                    );
                })
                .collect(Collectors.toList());
    }

    private WmPoiAggre getPoiInfo(String entityId) {
        WmPoiAggre wmPoiAggre = poiClient.queryWmPoiAggre(Long.valueOf(entityId), Sets.newHashSet("name", "pic_url"));
        return Optional.ofNullable(wmPoiAggre).orElse(new WmPoiAggre());
    }

    private void updateStatistics(String status, JobStatistics statistics) {
        statistics.incrementTotal();
        if (JobStatusEnum.SUCCESS.getCode().equals(status)) {
            statistics.incrementSuccess();
        } else if (JobStatusEnum.FAIL.getCode().equals(status)) {
            statistics.incrementFail();
        }
    }

    private JobListResponseDto buildJobListResponse(List<JobResponseDto> jobList, JobStatistics statistics) {
        return JobListResponseDto.builder()
                .jobList(jobList)
                .total(statistics.getTotal())
                .success(statistics.getSuccess())
                .fail(statistics.getFail())
                .build();
    }

    private RunningJobCountResponseDto buildRunningJobCountResponseDto(Long runnings, Boolean needToClick) {
        return RunningJobCountResponseDto.builder()
                .runnings(runnings)
                .needToClick(needToClick)
                .build();
    }

    private static class JobStatistics {
        private int total;
        private int success;
        private int fail;

        public void incrementTotal() {
            total++;
        }

        public void incrementSuccess() {
            success++;
        }

        public void incrementFail() {
            fail++;
        }

        public int getTotal() {
            return total;
        }

        public int getSuccess() {
            return success;
        }

        public int getFail() {
            return fail;
        }
    }


}
