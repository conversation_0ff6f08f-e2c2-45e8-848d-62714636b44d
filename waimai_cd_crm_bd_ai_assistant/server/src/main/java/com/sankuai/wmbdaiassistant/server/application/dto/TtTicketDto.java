package com.sankuai.wmbdaiassistant.server.application.dto;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TtTicketDto {

    private static final String STATE_NAME_PATH = "$.name";

    private static final List<String> JSON_PREFIX = Lists.newArrayList("{", "[");

    /**
     * TT的 id，唯一标识
     */
    @JSONField(alternateNames = "id")
    private Integer ticketId;

    /**
     * TT的 rg 标示 （Resolver Group）服务组单位
     * TT通过三级目录挂载 rg 的方式区分 rg
     */
    private Integer rgId;

    /**
     * TT的工单的名称
     */
    private String name;

    /**
     * TT工单创建人
     */
    @JSONField(alternateNames = "createdBy")
    private String creatorMis;

    /**
     * TT工单状态
     * {@link com.meituan.waimai.ka.skysea.tt.model.enums.TicketState}
     */
    private String state;

    /**
     * TT工单创建时间
     */
    @JSONField(alternateNames = "createdAt")
    private Date createTime;

    /**
     * TT工单更新时间
     */
    @JSONField(alternateNames = "updatedAt")
    private Date modifyTime;

    /**
     * 标签信息
     */
    private List<String> labels;

    public void setState(String state) {
        // 如果是 json ，则获取 json 里面的 name
        if (Objects.nonNull(state) && JSON_PREFIX.stream().anyMatch(state::startsWith)) {
            this.state = JSONPath.eval(state, STATE_NAME_PATH).toString();
            return;
        }

        this.state = state;
    }
}
