package com.sankuai.wmbdaiassistant.server.openapi.thrift;

import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.wmbdaiassistant.api.openapi.OpenapiHelpBDThriftService;
import com.sankuai.wmbdaiassistant.api.response.helpbd.ExtResponse;
import com.sankuai.wmbdaiassistant.server.ThriftPortConstant;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.TtHelpbdService;
import javax.annotation.Resource;

/**
 * 【开放平台】扩展能力
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/6/19 15:21
 */
@MdpThriftServer(port = ThriftPortConstant.PORT_9010)
public class OpenapiHelpBDThriftServiceImpl implements OpenapiHelpBDThriftService {
    @Resource
    private TtHelpbdService ttHelpbdService;

    @Override
    public ExtResponse<JsonNode> queryTicket(Integer ticketId) {
        JsonNode queryTicket = ttHelpbdService.queryTicket(ticketId);
        return ExtResponse.success(queryTicket);
    }
}
