package com.sankuai.wmbdaiassistant.server.application.enums;

/**
 * 返回码的枚举类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-02-28 10:37
 */
public enum ResultCodeEnum {

    SUCCESS(0, "成功"),

    SYSTEM_ERROR(-1, "系统错误"),

    ARGUMENT_ERROR(-2, "参数异常"),

    BUSINESS_ERROR(-3, "业务异常"),

    REMOTE_ERROR(-4, "第三方服务异常"),

    ;

    private final int code;
    private final String msg;

    ResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static ResultCodeEnum ofCode(int code) {
        for (ResultCodeEnum resultCode: ResultCodeEnum.values()) {
            if (resultCode.getCode() == code) {
                return resultCode;
            }
        }
        return null;
    }
}
