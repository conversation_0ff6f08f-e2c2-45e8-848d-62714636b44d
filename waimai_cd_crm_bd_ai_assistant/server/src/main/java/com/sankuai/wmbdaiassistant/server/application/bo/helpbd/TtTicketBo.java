package com.sankuai.wmbdaiassistant.server.application.bo.helpbd;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TtTicketBo {

    /**
     * 数据库唯一标示
     */
    private Integer id;

    /**
     * TT的 id，唯一标识
     */
    private Integer ticketId;

    /**
     * TT的 rg 标示 （Resolver Group）服务组单位
     * TT通过三级目录挂载 rg 的方式区分 rg
     */
    private Integer rgId;

    /**
     * TT的工单的名称
     */
    private String name;

    /**
     * TT工单创建人
     */
    private String creatorMis;

    /**
     * TT工单状态
     * {@link com.meituan.waimai.ka.skysea.tt.model.enums.TicketState}
     */
    private String state;

    /**
     * 关联的 ones
     */
    private String onesId;

    /**
     * ones 状态
     */
    private String onesStatus;

    /**
     * TT工单创建时间
     */
    private Date createTime;

    /**
     * TT工单更新时间
     */
    private Date modifyTime;

    /**
     * 数据库版本号
     */
    private Date version;

    /**
     * 标签列表
     */
    private List<String> labels;
}
