package com.sankuai.wmbdaiassistant.server.application.bo;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 大象群聊消息查询请求
 *
 * <AUTHOR>
 * @date 2024-12-13 17:21
 */
@Data
public class HistoryGroupMsgsRequest {

    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 大象群ID
     */
    private Long gid;
    /**
     * 消息类型
     */
    private List<Integer> messageTypeList;
    /**
     * 搜索关键词，key为搜索意图，value为所有关键词的相近词
     */
    private Map<String, Set<String>> searchKeyMap;


}

