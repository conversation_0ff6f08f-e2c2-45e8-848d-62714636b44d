package com.sankuai.wmbdaiassistant.server.application.service.rag;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.request.FaqAddRequestDto;
import com.sankuai.wmbdaiassistant.api.request.FaqModifyRequestDto;
import com.sankuai.wmbdaiassistant.api.response.StandardPhraseDetailResponseDto;

/**
 * RAG 服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-03 15:32
 */
public interface RagService {

    /**
     * FAQ增加
     *
     * @param requestDto 请求
     * @param user       当前用户
     * @return
     */
    Long add(FaqAddRequestDto requestDto, User user);

    /**
     * FAQ 修改
     *
     * @param requestDto 请求
     * @param user       当前用户
     * @return
     */
    boolean modify(FaqModifyRequestDto requestDto, User user);

    /**
     * 根据ID查询
     *
     * @param phraseId
     * @return
     */
    StandardPhraseDetailResponseDto detail(Long phraseId, User user);

    /**
     * 删除
     *
     * @param phraseId phraseId
     * @param user 当前用户
     * @return
     */
    boolean delete(Long phraseId, User user);

    /**
     * 启用
     *
     * @param phraseId phraseId
     * @param user 当前用户
     * @return 结果
     */
    boolean enable(Long phraseId, User user);

}
