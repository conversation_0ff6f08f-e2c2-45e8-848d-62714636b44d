package com.sankuai.wmbdaiassistant.server.application.service.dataset;

import com.sankuai.wmbdaiassistant.server.mq.event.WikiEvent;

/**
 * 知识变更处理
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-23 14:50
 */
public interface KnowledgeChangeService {

    /**
     * 通知wiki变更
     *
     * @param wikiEvent wiki事件
     */
    void notifyWikiChange(WikiEvent wikiEvent);

    /**
     * 处理wiki新增事件
     *
     * @param wikiEvent wiki事件
     */
    void handlerWikiAdd(WikiEvent wikiEvent);

    /**
     * 处理wiki更新事件
     *
     * @param wikiEvent wiki事件
     */
    void handlerWikiUpdate(WikiEvent wikiEvent);

    /**
     * 处理wiki删除事件
     *
     * @param wikiEvent wiki事件
     */
    void handlerWikiDelete(WikiEvent wikiEvent);
}
