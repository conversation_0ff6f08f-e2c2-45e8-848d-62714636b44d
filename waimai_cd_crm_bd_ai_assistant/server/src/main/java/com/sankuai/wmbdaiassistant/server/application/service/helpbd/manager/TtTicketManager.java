package com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager;

import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.dao.TtTicketPo;
import com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper.TtTicketDao;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.PageQueryBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtTicketBo;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class TtTicketManager {

    @Autowired
    private TtTicketDao dao;

    public void create(TtTicketBo bo) {
        TtTicketPo po = EnBeanUtils.copy(bo, TtTicketPo::new, (bo1, po1) -> {
            if (Objects.nonNull(bo1.getLabels())) {
                po1.setLabels(String.join(",", bo1.getLabels()));
            } else {
                po1.setLabels("");
            }
        });
        dao.insertOrUpdateSelective(po);
    }

    public void update(TtTicketBo bo) {
        TtTicketPo po = EnBeanUtils.copy(bo, TtTicketPo::new, (bo1, po1) -> {
            if (Objects.nonNull(bo1.getLabels())) {
                po1.setLabels(String.join(",", bo1.getLabels()));
            } else {
                po1.setLabels("");
            }
        });
        dao.updateByTicketIdSelective(po);
    }

    public TtTicketBo queryByTicketId(Integer ticketId) {
        TtTicketPo po = dao.selectByTicketId(ticketId);
        return EnBeanUtils.copy(po, TtTicketBo::new);
    }

    public TtTicketBo queryByOnesId(String onesId) {
        TtTicketPo po = dao.selectByOnesId(onesId);
        return EnBeanUtils.copy(po, TtTicketBo::new);
    }

    public List<TtTicketBo> queryUnOnesRefByPage(PageQueryBo pageQueryBo, Date startTime) {

        // 查询的时候多查一个
        Integer realPageSize = pageQueryBo.getPageSize() + 1;

        // 查询
        List<TtTicketPo> pos = dao.selectUnOnesRefByPage(pageQueryBo.getMinId(), realPageSize, startTime);

        // 根据多查询一个的 size 来看是否需要下一次查询
        boolean more = CollectionUtils.isNotEmpty(pos) && realPageSize.equals(pos.size());
        pageQueryBo.setMore(more);

        // 返回的时候把最后一个去掉
        if (more) {
            pos.remove(pos.size() - 1);
            // 更新下一次查询的游标
            pageQueryBo.setMinId(pos.get(pos.size() - 1).getId());
        }

        return EnBeanUtils.copyList(pos, TtTicketBo::new);
    }

    public TtTicketBo query(Integer id) {
        TtTicketPo po = dao.selectByPrimaryKey(id);
        return EnBeanUtils.copy(po, TtTicketBo::new);
    }


}
