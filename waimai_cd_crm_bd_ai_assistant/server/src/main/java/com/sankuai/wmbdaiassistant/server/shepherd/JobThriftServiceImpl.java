package com.sankuai.wmbdaiassistant.server.shepherd;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;
import com.sankuai.wmbdaiassistant.api.response.RunningJobCountResponseDto;
import com.sankuai.wmbdaiassistant.api.service.JobThriftService;
import com.sankuai.wmbdaiassistant.common.DateUtil;
import com.sankuai.wmbdaiassistant.common.ExecutorUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.enums.JobTypeEnum;
import com.sankuai.wmbdaiassistant.domain.model.TraceLogModel;
import com.sankuai.wmbdaiassistant.domain.service.common.trace.TraceLogService;
import com.sankuai.wmbdaiassistant.server.application.service.job.JobService;
import com.sankuai.wmbdaiassistant.server.filter.ChatSessionFilter;
import com.sankuai.wmbdaiassistant.server.filter.ExceptionHanderFilter;
import com.sankuai.wmbdaiassistant.server.filter.UserFilter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:16
 */
@Slf4j
@MdpThriftServer(port = 9012)
@MdpThriftServerExtConfig(filters = {UserFilter.class, ExceptionHanderFilter.class, ChatSessionFilter.class})
public class JobThriftServiceImpl implements JobThriftService {

    @Resource
    private JobService jobService;

    @Resource
    private TraceLogService traceLogService;

    @MdpConfig("disableGetPoiDiagnosisRunningJobsNum:false")
    private Boolean disableGetPoiDiagnosisRunningJobsNum;

    @Override
    public BeeResponseDto listPoiDiagnosisJobsToday(Long sessionId) {
        String mis = getMis();
        Date startDate = DateUtil.getTodayFirstSecond();
        Date endDate = DateUtil.getTodayLastSecond();
        return BeeResponseDto.buildSuccessResponse(
                jobService.getJobs(mis, sessionId, JobTypeEnum.POI_DIAGNOSIS.getCode(), startDate, endDate));
    }


    @Override
    public BeeResponseDto countPoiDiagnosisTodayRunningJobs() {
        String mis = getMis();
        Date startDate = DateUtil.getTodayFirstSecond();
        Date endDate = DateUtil.getTodayLastSecond();

        RunningJobCountResponseDto responseDto;
        if (disableGetPoiDiagnosisRunningJobsNum) {
            responseDto = new RunningJobCountResponseDto();
            responseDto.setRunnings(0L);
            responseDto.setNeedToClick(false);
        } else {
            responseDto = jobService.getPoiDiagnosisRunningJobsNum(mis, startDate, endDate);
        }

        return BeeResponseDto.buildSuccessResponse(responseDto);
    }

    @Override
    public BeeResponseDto recordPopup() {
        String mis = getMis();
        ExecutorUtil.safeExecute(() -> traceLogService.insert(TraceLogModel.buildRecordPopupTraceLog(mis)));
        return BeeResponseDto.buildSuccessResponse();
    }


    private String getMis() {
        User user = UserUtils.getUser();
        ParamCheckUtil.notNull(user, "获取用户信息失败");
        return user.getLogin();
    }
}
