package com.sankuai.wmbdaiassistant.server.application.service.helpbd;

import com.sankuai.wmbdaiassistant.server.application.dto.MosesCommentStarConfigDto;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.dto.UserDto;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentConfigBo;
import java.util.List;

/**
 * <AUTHOR> <wb_liu<PERSON><EMAIL>>
 * @date 2025/6/19 20:08
 */
public interface CommentService {

    /**
     * 摩西评论
     *
     * @param mosesCommentBo
     */
    void mosesComment(MosesCommentBo mosesCommentBo);

    /**
     * 获取摩西评论配置
     *
     * @param user
     * @return
     */
    List<MosesCommentStarConfigDto> getMosesStarCommentConfig(UserDto user, MosesCommentConfigBo mosesCommentConfigBo);

}
