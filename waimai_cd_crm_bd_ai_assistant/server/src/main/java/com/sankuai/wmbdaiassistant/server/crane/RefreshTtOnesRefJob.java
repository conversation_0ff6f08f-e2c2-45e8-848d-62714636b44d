package com.sankuai.wmbdaiassistant.server.crane;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.OnesInfoBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.PageQueryBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtOnesRelationBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TtTicketBo;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.manager.TtTicketManager;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.OnesWrapper;
import com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper.TtWrapper;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 补偿任务
 * 每晚 12 点刷新 14 天以内的tt工单，查看其是否已经关联上 ones ，以及更新 ones 状态
 *
 * <AUTHOR>
 */
@Slf4j
@CraneConfiguration
public class RefreshTtOnesRefJob {

    /**
     * 往前查询默认 14 天的记录
     */
    private static final Long DEFAULT_BEFORE_TIME = 1000 * 60 * 60 * 24 * 14L;

    @Autowired
    private TtTicketManager ttTicketManager;

    @Autowired
    private TtWrapper ttWrapper;

    @Autowired
    private OnesWrapper onesWrapper;

    @Crane("refreshTtOnesRefresh")
    public void refreshTtOnesRef(String args) {
        log.info("refreshTtOnesRef start with args :{}", args);

        Long beforeTime = DEFAULT_BEFORE_TIME;

        // 处理自定义参数
        try {
            if (StringUtils.isNotEmpty(args)) {
                JobArgs jobArgs = JSON.parseObject(args, JobArgs.class);
                if (Objects.nonNull(jobArgs.getBeforeTime())) {
                    beforeTime = jobArgs.getBeforeTime();
                }
            }
        } catch (Exception e) {
            log.error("job args parse error");
        }

        Date startTime = new Date(System.currentTimeMillis() - beforeTime);

        PageQueryBo pageQueryBo = new PageQueryBo();
        while (pageQueryBo.hasNext()) {
            List<TtTicketBo> ttTicketList = ttTicketManager.queryUnOnesRefByPage(pageQueryBo, startTime);
            ttTicketList.forEach(this::refresh);
        }

        log.info("refreshTtOnesRef start end");
    }

    private void refresh(TtTicketBo ttTicket) {
        try {
            TtOnesRelationBo ttOnesRelation = ttWrapper.getTtOnesRelation(ttTicket.getTicketId());
            if (Objects.isNull(ttOnesRelation)) {
                return;
            }
            ttTicket.setOnesId(ttOnesRelation.getOnesId());

            OnesInfoBo onesInfo = onesWrapper.getOnesInfo(ttOnesRelation.getOnesId());
            ttTicket.setOnesStatus(onesInfo.getState());

            ttTicketManager.update(ttTicket);
        } catch (Exception e) {
            log.warn("ones or tt rpc error, ticketId:{}", ttTicket.getTicketId(), e);
        }
    }

    @Data
    @NoArgsConstructor
    private static class JobArgs {
        private Long beforeTime;
    }


}
