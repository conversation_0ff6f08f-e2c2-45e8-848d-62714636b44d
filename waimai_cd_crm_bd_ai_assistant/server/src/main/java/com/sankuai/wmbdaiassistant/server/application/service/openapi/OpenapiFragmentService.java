package com.sankuai.wmbdaiassistant.server.application.service.openapi;

import com.sankuai.wmbdaiassistant.api.request.RecallRequestDto;
import com.sankuai.wmbdaiassistant.api.response.RecallResponseDto;

/**
 * 【开放平台】分片召回服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-10 19:26
 */
public interface OpenapiFragmentService {

    /**
     * 分片召回
     *
     * @param request 请求
     * @return
     */
    RecallResponseDto recall(RecallRequestDto request);

}
