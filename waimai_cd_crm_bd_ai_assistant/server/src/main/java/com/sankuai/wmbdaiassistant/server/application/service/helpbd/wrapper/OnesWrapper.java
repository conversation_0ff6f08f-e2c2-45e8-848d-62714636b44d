package com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcTimeoutException;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.OnesInfoBo;
import com.sankuai.wmbdaiassistant.server.application.dto.OnesApiBaseDto;
import com.sankuai.wmbdaiassistant.server.application.dto.OnesInfoDto;
import com.sankuai.wmbdaiassistant.server.application.utils.EnBeanUtils;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OnesWrapper {

    private static final int CONNECT_TIMEOUT = 1;
    private static final int READ_TIMEOUT = 2;

    private static final String ONES_INFO_URL = "/api/1.0/ones/issue/{}";

    private static final String BASIC_AUTH_HEADER = "Authorization";
    private static final String AUTH_MIS_HEADER = "USERNAME";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String JSON_CONTENT = "application/json";

    private static final String AUTH = "Basic aXRfY3JtX3NhbGU6aXRfY3JtX3NhbGVfb25lcw==";
    private static final String AUTH_MIS = "it_crm_sale";

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .build();

    public OnesInfoBo getOnesInfo(String oneId) {
        try {
            OnesInfoDto onesInfoDto = get(StringUtil.of(HelpBDConfig.ONES_API_HOST + ONES_INFO_URL, oneId), getSuperUserHeader(), new TypeReference<OnesApiBaseDto<OnesInfoDto>>() {
            });
            return EnBeanUtils.copy(onesInfoDto, OnesInfoBo::new);
        } catch (Exception e) {
            log.error("getOnesInfo error, onesId is {}", oneId, e);
            return null;
        }
    }

    private <T> T get(String url, Map<String, String> headerMap, TypeReference<OnesApiBaseDto<T>> type) {
        Request.Builder builder = new Request.Builder();

        if (Objects.nonNull(headerMap)) {
            for (String key : headerMap.keySet()) {
                builder.addHeader(key, headerMap.get(key));
            }
        }
        Request request = builder.url(url).build();
        return executeCall(request, type, url);
    }

    private <T> T executeCall(Request request, TypeReference<OnesApiBaseDto<T>> type, String url) {
        try (Response resp = client.newCall(request).execute()) {
            if (!resp.isSuccessful()) {
                throw new RpcServerException(StringUtil.of("ones api http code: {}, url is {}", resp.code(), url));
            }

            String body = resp.body().string();
            OnesApiBaseDto<T> baseDto = JSON.parseObject(body, type);
            if (baseDto.isFailure()) {
                throw new RpcServerException(StringUtil.of("ones api error ,body is {}, url is {}", body, url));
            }
            return baseDto.getData();
        } catch (IOException e) {
            log.error("IOException occurred while executing call to URL: {}", url, e);
            throw new RpcTimeoutException(StringUtil.of("ones api error, url is {}", url), e);
        }
    }

    private Map<String, String> getSuperUserHeader() {
        HashMap<String, String> header = Maps.newHashMap();
        header.put(BASIC_AUTH_HEADER, AUTH);
        header.put(AUTH_MIS_HEADER, AUTH_MIS);
        header.put(CONTENT_TYPE, JSON_CONTENT);
        return header;
    }

    public static void main(String[] args) {

        OnesWrapper onesWrapper = new OnesWrapper();
        onesWrapper.setCallBack(Lists.newArrayList("17249"));
//        onesWrapper.deleteCallBack(Lists.newArrayList("2418"));
//        OnesInfoBo onesInfo = onesWrapper.getOnesInfo("1234564567653");
//        System.out.println(onesInfo);
    }

    private void setCallBack(List<String> issueList) {
        String callBack = "http://waimai-eci-201110-153755-410-sl-skysea.waimai.test.sankuai.com/web-hook/onesUpdate";

        MediaType appJson = MediaType.parse("application/json;charset=utf-8");

        for (String issue : issueList) {
            JSONObject body = new JSONObject();
            JSONObject authContent = new JSONObject();
            authContent.put("token", AUTH);
            body.put("authContent", authContent);
            body.put("authType", "token");
            body.put("objectId", issue);
            body.put("rollBackUrl", callBack);
            body.put("type", "DEVTASK");
            Request req = new Request.Builder()
                    .url("http://ones.vip.sankuai.com/api/1.0/ones/webhook")
                    .addHeader(BASIC_AUTH_HEADER, AUTH)
                    .addHeader(AUTH_MIS_HEADER, AUTH_MIS)
                    .addHeader(CONTENT_TYPE, JSON_CONTENT)
                    .post(RequestBody.create(appJson, body.toString()))
                    .build();
            String s = executeCall(req, new TypeReference<OnesApiBaseDto<String>>() {
            }, null);
            System.out.println(s);
        }
    }

    private void deleteCallBack(List<String> callBackIdList) {
        for (String callBackId : callBackIdList) {
            Request req = new Request.Builder()
                    .url("http://ones.vip.sankuai.com/api/1.0/ones/webhook/" + callBackId)
                    .addHeader(BASIC_AUTH_HEADER, AUTH)
                    .addHeader(AUTH_MIS_HEADER, AUTH_MIS)
                    .addHeader(CONTENT_TYPE, JSON_CONTENT)
                    .delete()
                    .build();
            String s = executeCall(req, new TypeReference<OnesApiBaseDto<String>>() {
            }, null);
            System.out.println(s);
        }
    }
}
