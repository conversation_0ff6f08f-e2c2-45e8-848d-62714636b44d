package com.sankuai.wmbdaiassistant.server.application.service.helpbd.wrapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.dto.WmNoticeDto;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmNoticeThriftService;
import com.sankuai.wmbdaiassistant.common.StringUtil;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcServerException;
import com.sankuai.wmbdaiassistant.common.exception.helpbd.RpcTimeoutException;
import com.sankuai.wmbdaiassistant.server.config.HelpBDConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NoticeWrapper {

    /**
     * 发送人。0表示系统发送消息
     */
    private static final int SENDER = 0;

    /**
     * 发布方式。分为：1按照人员名单，2组织架构选择，3外卖全体员工，4配送全体员工
     */
    private static final byte PUSH_TYPE = 1;

    /**
     * 消息内容格式。分为：0自定义，1单图文，2多图文，3文字类型 (文字类型html仅能解析<br><a>)
     */
    private static final byte MSG_FORMAT = 3;

    /**
     * 是否必读,0代表否
     */
    private static final byte MUST_READ = 0;

    /**
     * 推送方式 接收端id序列，以逗号隔开。1先富，2大象，3蜜蜂推送，4短信，5邮件，如“1,2,3”。
     */
    private static final String PUSH_MEDIA = "3";

    /**
     * 是否有效。1有效
     * 消息推送服务是先入库，然后推送。这个是标示是否有效
     */
    private static final byte VALID = 1;

    private final WmNoticeThriftService.Iface client;

    @Autowired
    public NoticeWrapper(WmNoticeThriftService.Iface wmNoticeThriftService) {
        this.client = wmNoticeThriftService;
    }

    public void send2Bee(Integer receiverUid, String title, String content) {
        WmNoticeDto result = new WmNoticeDto();
        result.setUid(SENDER);
        result.setPushType(PUSH_TYPE);
        result.setReceiveUids(String.valueOf(receiverUid));
        result.setMsgFormat(MSG_FORMAT);
        result.setMustRead(MUST_READ);
        result.setPushMedia(PUSH_MEDIA);
        result.setValid(VALID);
        result.setWmNoticeTypeId(HelpBDConfig.NOTICE_TYPE);

        JSONObject msg = new JSONObject();
        msg.put("title",title);
        msg.put("content",content);
        JSONArray array = new JSONArray();
        array.add(msg);
        result.setMsg(array.toJSONString());

        try {
            client.saveNotice(result);
        } catch (WmServerException e) {
            throw new RpcServerException(StringUtil.of("调用消息中心出错 ,uid:{} , msg:{}", receiverUid, msg), e);
        } catch (TException e) {
            throw new RpcTimeoutException(StringUtil.of("调用消息中心超时 ,uid:{} , msg:{}", receiverUid, msg), e);
        }
    }
}
