package com.sankuai.wmbdaiassistant.server.manage;

import com.sankuai.wmbdaiassistant.api.response.WebResponseDto;
import com.sankuai.wmbdaiassistant.api.response.hotquestion.HotQuestionConfigSaveRequestDto;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigBo;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigItem;
import com.sankuai.wmbdaiassistant.domain.bo.HotQuestionConfigSaveRequestBo;
import com.sankuai.wmbdaiassistant.domain.service.hotquestion.HotQuestionService;
import com.sankuai.wmbdaiassistant.server.application.bo.HotQuestionConfigBeanCopy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通用 controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping({"/manage/hotQuestion"})
public class HotQuestionConfigController {

    @Autowired
    private HotQuestionService hotQuestionService;

    @Autowired
    private HotQuestionConfigBeanCopy hotQuestionConfigBeanCopy;

    @GetMapping("/queryAllAssistantBiz")
    public WebResponseDto<Set<String>> queryAllAssistantBiz() {
        return WebResponseDto.of(hotQuestionService.getAllAssistantBiz());
    }

    @GetMapping("/queryHotQuestionConfig")
    public WebResponseDto<HotQuestionConfigBo> queryHotQuestionConfig(String assistantBiz) {
        return WebResponseDto.of(hotQuestionService.getConfigByAssistantBiz(assistantBiz));
    }

    //保存配置
    @PostMapping("/saveHotQuestionConfig")
    public WebResponseDto<Void> saveHotQuestionConfig(@RequestBody HotQuestionConfigSaveRequestDto requestDto) {

        hotQuestionService.saveConfigByAssistantBiz(toHotQuestionConfigSaveRequestBo(requestDto));
        return WebResponseDto.success();
    }

    private HotQuestionConfigSaveRequestBo toHotQuestionConfigSaveRequestBo(HotQuestionConfigSaveRequestDto requestDto) {
        if (requestDto == null) {
            return null;
        }
        HotQuestionConfigSaveRequestBo bo = new HotQuestionConfigSaveRequestBo();
        bo.setAssistantBiz(requestDto.getAssistantBiz());
        if (CollectionUtils.isNotEmpty(requestDto.getConfigItemList())) {
            List<HotQuestionConfigItem> configItemList = requestDto.getConfigItemList().stream().map(itemDto -> hotQuestionConfigBeanCopy.configDtoToBo(itemDto)).collect(Collectors.toList());
            bo.setConfigItemList(configItemList);
        }
        return bo;
    }

}
