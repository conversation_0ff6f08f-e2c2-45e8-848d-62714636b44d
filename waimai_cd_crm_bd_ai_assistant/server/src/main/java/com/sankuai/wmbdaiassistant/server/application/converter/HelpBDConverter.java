package com.sankuai.wmbdaiassistant.server.application.converter;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeEntryTrackingDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeMosesCommentRequestDto;
import com.sankuai.wmbdaiassistant.api.request.helpbd.BeeUserDto;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.EntryTrackingTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.MosesCommentTypeEnum;
import com.sankuai.wmbdaiassistant.domain.enums.helpbd.RobotType;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.EntryTrackingBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.MosesCommentBo;
import com.sankuai.wmbdaiassistant.server.application.bo.helpbd.TenantBo;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-07-04
 */
public class HelpBDConverter {

    public static EntryTrackingBo toBoWhenUser(User user, BeeEntryTrackingDto beeEntryTrackingDto) {

        EntryTrackingBo bo = new EntryTrackingBo();
        bo.setMisId(user.getLogin());
        bo.setUid((long) user.getId());
        bo.setSource(EntryTrackingTypeEnum.getByCode(beeEntryTrackingDto.getSource()));
        bo.setRobotType(beeEntryTrackingDto.getRobotType());
        bo.setRobotId(beeEntryTrackingDto.getRobotId());

        TenantBo tenantBo = new TenantBo();
        tenantBo.setTenantId(beeEntryTrackingDto.getTenantId());
        tenantBo.setTenantName(beeEntryTrackingDto.getTenantName());
        tenantBo.setBizId(beeEntryTrackingDto.getBizId());
        tenantBo.setBizName(beeEntryTrackingDto.getBizName());
        bo.setTenant(tenantBo);

        return bo;
    }

    public static EntryTrackingBo toBoWhenBeeUser(BeeUserDto userDto, BeeEntryTrackingDto beeEntryTrackingDto) {

        EntryTrackingBo bo = new EntryTrackingBo();

        bo.setMisId(userDto.getMisId());
        bo.setUid(userDto.getUid());
        bo.setSource(EntryTrackingTypeEnum.getByCode(beeEntryTrackingDto.getSource()));
        //兼容老版本
        if (beeEntryTrackingDto.getRobotType() == null) {
            bo.setRobotType(RobotType.MOSES.getCode());
        } else {
            bo.setRobotType(beeEntryTrackingDto.getRobotType());
        }
        bo.setRobotId(beeEntryTrackingDto.getRobotId());

        TenantBo tenantBo = new TenantBo();
        tenantBo.setTenantId(beeEntryTrackingDto.getTenantId());
        tenantBo.setTenantName(beeEntryTrackingDto.getTenantName());
        tenantBo.setBizId(beeEntryTrackingDto.getBizId());
        tenantBo.setBizName(beeEntryTrackingDto.getBizName());
        bo.setTenant(tenantBo);

        return bo;
    }

    public static MosesCommentBo toBoWithMoses(BeeUserDto beeUser, BeeMosesCommentRequestDto mosesCommentRequestDto) {
        MosesCommentBo commentBo = new MosesCommentBo();
        commentBo.setMisId(beeUser.getMisId());
        commentBo.setUid(beeUser.getUid());

        TenantBo tenantBo = new TenantBo();
        tenantBo.setTenantId(mosesCommentRequestDto.getTenantId());
        tenantBo.setTenantName(mosesCommentRequestDto.getTenantName());
        tenantBo.setBizId(mosesCommentRequestDto.getBizId());
        tenantBo.setBizName(mosesCommentRequestDto.getBizName());

        commentBo.setTenant(tenantBo);
        //兼容老版本
        if (mosesCommentRequestDto.getRobotType() == null) {
            commentBo.setRobotType(RobotType.MOSES.getCode());
        } else {
            commentBo.setRobotType(mosesCommentRequestDto.getRobotType());
        }
        commentBo.setRobotId(mosesCommentRequestDto.getRobotId());
        commentBo.setStar(mosesCommentRequestDto.getStar());
        commentBo.setValue(mosesCommentRequestDto.getValue());
        commentBo.setDesc(mosesCommentRequestDto.getDesc());
        commentBo.setTips(mosesCommentRequestDto.getTips());
        commentBo.setCreatedTime(new Date());
        commentBo.setComment(mosesCommentRequestDto.getComment());
        commentBo.setType(MosesCommentTypeEnum.getByCode(mosesCommentRequestDto.getType()));
        return commentBo;
    }
}
