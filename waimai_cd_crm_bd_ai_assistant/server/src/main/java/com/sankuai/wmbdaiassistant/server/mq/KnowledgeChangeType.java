package com.sankuai.wmbdaiassistant.server.mq;

import lombok.Getter;

/**
 * 知识变更的类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-06-23 10:26
 */
@Getter
public enum KnowledgeChangeType {
    WIKI_ADD("wiki_add"),
    WIKI_UPDATE("wiki_update"),
    WIKI_DELETE("wiki_delete"),
    ;

    private String code;

    KnowledgeChangeType(String code) {
        this.code = code;
    }

    public static KnowledgeChangeType getByCode(String code) {
        for (KnowledgeChangeType value : KnowledgeChangeType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
