package com.sankuai.wmbdaiassistant.server.application.service.dsl;

import com.sankuai.wmbdaiassistant.server.application.dto.PoiDiagnosticReadyDto;

import java.util.List;
import java.util.Map;

/**
 * 商家诊断技能（供 DSL 调用）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-11
 */
public interface PoiDiagnoseDslService {


    /**
     * 触发商家诊断
     *
     * @param poiIdList        商家id列表
     * @param uid              用户id
     * @return     Map<商家id, 诊断结果就绪信息>
     */
    Map<Long, PoiDiagnosticReadyDto> triggerPoiDiagnostic(List<Long> poiIdList, Integer uid, String mis);
}
