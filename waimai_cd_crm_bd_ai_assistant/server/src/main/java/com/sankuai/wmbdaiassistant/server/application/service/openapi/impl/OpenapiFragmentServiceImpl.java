package com.sankuai.wmbdaiassistant.server.application.service.openapi.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.wmbdaiassistant.api.request.RecallRequestDto;
import com.sankuai.wmbdaiassistant.api.response.RecallFragmentDto;
import com.sankuai.wmbdaiassistant.api.response.RecallResponseDto;
import com.sankuai.wmbdaiassistant.common.DefaultUtil;
import com.sankuai.wmbdaiassistant.common.ParamCheckUtil;
import com.sankuai.wmbdaiassistant.domain.model.FragmentCoreModel;
import com.sankuai.wmbdaiassistant.domain.repository.FragmentCoreRepository;
import com.sankuai.wmbdaiassistant.domain.repository.query.FragmentCoreQuery;
import com.sankuai.wmbdaiassistant.server.application.service.openapi.OpenapiConfig;
import com.sankuai.wmbdaiassistant.server.application.service.openapi.OpenapiFragmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 【OpenAPI】片段服务
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-07-10 19:27
 */
@Slf4j
@Service
public class OpenapiFragmentServiceImpl implements OpenapiFragmentService {

    @Resource
    private OpenapiConfig openapiConfig;

    @Resource
    private FragmentCoreRepository fragmentCoreRepository;

    @MdpConfig("dataset.code.map:{\"xc_shenhuiyuan\":8}")
    private HashMap<String, Long> datasetCodeMap;

    @MdpConfig("recall.max.size:200")
    private int recallMaxSize;

    @Override
    public RecallResponseDto recall(RecallRequestDto request) {
        ParamCheckUtil.notNull(request, "请求不能为空");
        ParamCheckUtil.notBlank(request.getApp(), "app不能为空");
        ParamCheckUtil.notBlank(request.getQuery(), "query不能为空");
        ParamCheckUtil.isTrue(openapiConfig.allowAppList.contains(request.getApp()), "当前app不被允许请联系开发");
        ParamCheckUtil.isTrue(CollectionUtils.isNotEmpty(request.getDatasetIds())
                || CollectionUtils.isNotEmpty(request.getDatasetCodes()), "datasetIds和datasetCodes不能同时为空");
        ParamCheckUtil.isTrue(request.getSize() == null || request.getSize() <= recallMaxSize
                , String.format("size不能超过%d", recallMaxSize));

        List<Long> datasetIds = request.getDatasetIds();
        if (CollectionUtils.isEmpty(datasetIds)) {
            datasetIds = DefaultUtil.defaultList(request.getDatasetCodes()).stream()
                    .map(datasetCodeMap::get)
                    .collect(Collectors.toList());
        }

        ParamCheckUtil.isTrue(CollectionUtils.size(datasetIds) == 1, "datasetIds只能有一个");

        FragmentCoreQuery query = FragmentCoreQuery.builder()
                .datasetId(datasetIds.get(0))
                .keyword(request.getQuery())
                .pageNum(1)
                .pageSize(DefaultUtil.defaultValue(request.getSize(), 5))
                .build();

        List<FragmentCoreModel> dataList = recall(query);

        RecallResponseDto response = new RecallResponseDto();
        response.setFragmentList(buildResponse(dataList,datasetIds.get(0)));
        return response;
    }

    private List<RecallFragmentDto> buildResponse(List<FragmentCoreModel> dataList,Long datasetId) {
        return DefaultUtil.defaultList(dataList).stream().map(fragmentModel -> {
            RecallFragmentDto fragmentDto = new RecallFragmentDto();
            fragmentDto.setContent(fragmentModel.getContent());
            fragmentDto.setScore(fragmentModel.getScore());
            fragmentDto.setId(fragmentModel.getId());
            fragmentDto.setTitle(fragmentModel.getTitle());
            fragmentDto.setWikiId(fragmentModel.getWikiId());
            fragmentDto.setChunkId(fragmentModel.getChunkId());
            fragmentDto.setTags(fragmentModel.getTags());
            // 设置数据集ID
            fragmentDto.setDatasetId(datasetId);
            return fragmentDto;
        }).collect(Collectors.toList());
    }

    private List<FragmentCoreModel> recall(FragmentCoreQuery query) {
        Integer pageNum = query.getPageNum();
        Integer pageSize = query.getPageSize();

        // 默认查两页数据
        query.setPageSize(pageSize * 2);

        List<FragmentCoreModel> dataList = new ArrayList<>();
        Set<String> contentList = new HashSet<>();

        while ((pageNum - 1) * pageSize < recallMaxSize && dataList.size() < pageSize) {

            List<FragmentCoreModel> fragmentList = fragmentCoreRepository.findByQuery(query);
            DefaultUtil.defaultList(fragmentList).forEach(fragment -> {
                if (contentList.contains(fragment.getContent())) {
                    return;
                }
                contentList.add(fragment.getContent());
                if (dataList.size() < pageSize) {
                    dataList.add(fragment);
                }
            });
            // 如果查询结果小于分页大小，已经查到最后了
            if (CollectionUtils.size(fragmentList) < pageSize) {
                break;
            }
            pageNum = pageNum + 1;
            query.setPageNum(pageNum);
        }

        return dataList;
    }
}
