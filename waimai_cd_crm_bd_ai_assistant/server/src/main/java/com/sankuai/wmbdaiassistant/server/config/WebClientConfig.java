package com.sankuai.wmbdaiassistant.server.config;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.JettyClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-22 14:16
 */
@Configuration
public class WebClientConfig {

    @Bean
    public ExecutorService executorService() {
        return Rhino
                .newThreadPool("webClientThreadPool", DefaultThreadPoolProperties.Setter().withCoreSize(100)
                        .withMaxSize(1000).withTraceable(true).withKeepAliveTimeMinutes(1).withMaxQueueSize(1000))
                .getExecutor();
    }

    @Bean
    public WebClient webClient(ExecutorService executorService) {
        SslContextFactory.Client sslContextFactory = new SslContextFactory.Client();
        HttpClient httpClient = new HttpClient(sslContextFactory);
        httpClient.setExecutor(executorService);
        // 配置连接池
        httpClient.setMaxConnectionsPerDestination(200); // 每个目标主机的最大连接数
        httpClient.setIdleTimeout(60000); // 空闲连接超时时间（60秒）
        httpClient.setConnectTimeout(60000); // 连接超时时间（5秒）
        httpClient.setMaxRequestsQueuedPerDestination(1000); // 每个目标主机的最大排队请求数
        return WebClient.builder().clientConnector(new JettyClientHttpConnector(httpClient)).build();
    }

}
