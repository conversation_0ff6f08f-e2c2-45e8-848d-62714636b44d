package com.sankuai.wmbdaiassistant.server.application.service.helpbd;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.wmbdaiassistant.api.response.helpbd.PersonTtInfoDto;

/**
 * TT 工单系统相关服务
 * <AUTHOR> <wb_l<PERSON><PERSON><PERSON><PERSON>@meituan.com>
 * @date 2025/6/19 16:18
 */
public interface TtHelpbdService {

    /**
     * 获取用户个人相关信息
     *
     * @param uid   用户标识
     * @param misId mis
     * @return 用户相关 TT 工单数据信息
     */
    PersonTtInfoDto getPersonalInfo(int uid, String misId);

    /**
     * 获取tt详细信息
     *
     * @param ticketId tt的唯一标识
     * @return tt详细信息
     */
    JsonNode queryTicket(Integer ticketId);

}
