# MDP DOCS=https://km.sankuai.com/custom/onecloud/page/1302115131

# 数据库配置-bdaiassistant
mdp.zebra[0].basePackage=com.sankuai.wmbdaiassistant.infrastructure.dal.mapper
mdp.zebra[0].mapperLocations=classpath:mappers/*.xml
mdp.zebra[0].initialPoolSize=5
mdp.zebra[0].minPoolSize=5
mdp.zebra[0].maxPoolSize=20
mdp.zebra[0].jdbcRef=bdaiassistant_bdaiassistant_product
mdp.zebra[0].dataSourceType=group
mdp.zebra[0].extraJdbcUrlParams=zeroDateTimeBehavior=convertToNull
# 设置默认事务处理器
mdp.zebra[0].useTransactionAsDefault=true
mdp.zebra[0].transactionName=mdpTM1

# 数据库配置-skysea
mdp.zebra[1].poolType=druid
mdp.zebra[1].basePackage=com.sankuai.wmbdaiassistant.infrastructure.dal.helpbd.mapper
mdp.zebra[1].mapperLocations=classpath:helpbd/mappers/*.xml
mdp.zebra[1].initialPoolSize=5
mdp.zebra[1].minPoolSize=5
mdp.zebra[1].maxPoolSize=20
mdp.zebra[1].jdbcRef=skysea_product
mdp.zebra[1].transactionName=mdpTM2
