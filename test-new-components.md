# 新组件测试指南

本文档说明如何运行为新实现的外呼功能组件编写的测试用例。

## 测试文件概览

### React Native 组件测试

1. **PieChart 组件测试**
   - 文件位置: `waimai_cd_fe_bee_assistant/src/components/PieChart/__tests__/PieChart.test.tsx`
   - 测试内容: 饼图组件的渲染、数据处理、图例显示等功能

2. **AICallRecord 组件测试**
   - 文件位置: `waimai_cd_fe_bee_assistant/src/components/AICallRecord/__tests__/AICallRecord.test.tsx`
   - 测试内容: 外呼任务卡片的状态显示、展开收起、进度条等功能

3. **集成测试**
   - 文件位置: `waimai_cd_fe_bee_assistant/src/components/MessageBox/Answer/AnswerContent/__tests__/textMessageUtils.integration.test.tsx`
   - 测试内容: 新组件在消息渲染系统中的集成情况

### React Web 组件测试

1. **PieChart 组件测试**
   - 文件位置: `waimai_cd_fe_crm_bdservice/src/pages/knowledge/chat/common/ui/PieChart/__tests__/PieChart.test.tsx`
   - 测试内容: Web版饼图组件的SVG渲染、样式应用等功能

2. **AICallRecord 组件测试**
   - 文件位置: `waimai_cd_fe_crm_bdservice/src/pages/knowledge/chat/common/ui/AICallRecord/__tests__/AICallRecord.test.tsx`
   - 测试内容: Web版外呼任务卡片的Antd组件集成、交互功能等

## 运行测试

### React Native 项目测试

```bash
# 进入 React Native 项目目录
cd waimai_cd_fe_bee_assistant

# 运行所有新组件测试
npm test -- --testPathPattern="(PieChart|AICallRecord|textMessageUtils.integration)"

# 运行单个组件测试
npm test -- PieChart.test.tsx
npm test -- AICallRecord.test.tsx
npm test -- textMessageUtils.integration.test.tsx

# 运行测试并生成覆盖率报告
npm test -- --coverage --testPathPattern="(PieChart|AICallRecord)"
```

### React Web 项目测试

```bash
# 进入 React Web 项目目录
cd waimai_cd_fe_crm_bdservice

# 运行所有新组件测试
npm test -- --testPathPattern="(PieChart|AICallRecord)"

# 运行单个组件测试
npm test -- PieChart.test.tsx
npm test -- AICallRecord.test.tsx

# 运行测试并生成覆盖率报告
npm test -- --coverage --testPathPattern="(PieChart|AICallRecord)"
```

## 测试覆盖的功能点

### PieChart 组件测试

- ✅ 基本渲染功能
- ✅ 数据处理和百分比计算
- ✅ 图例显示和隐藏
- ✅ 自定义尺寸支持
- ✅ 空数据处理
- ✅ 单项数据处理
- ✅ 零值数据处理
- ✅ 样式和CSS类应用

### AICallRecord 组件测试

- ✅ 基本信息显示
- ✅ 不同状态的正确显示
- ✅ 展开收起功能
- ✅ 进度条显示
- ✅ 统计数据展示
- ✅ 详细信息显示
- ✅ 可选描述信息处理
- ✅ 边界情况处理（0%、100%进度）

### 集成测试

- ✅ 消息渲染系统集成
- ✅ 不同消息类型处理
- ✅ 键值生成正确性
- ✅ 未知消息类型处理

## 测试数据示例

### PieChart 测试数据

```typescript
const mockData = [
    { label: '成功', value: 150, color: '#52c41a' },
    { label: '失败', value: 50, color: '#ff4d4f' },
    { label: '待处理', value: 30, color: '#faad14' },
];
```

### AICallRecord 测试数据

```typescript
const mockData = {
    taskId: 'task-123',
    taskName: '测试外呼任务',
    status: 'running',
    progress: 65,
    totalCount: 1000,
    completedCount: 650,
    successCount: 500,
    failedCount: 150,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 14:20:00',
    description: '这是一个测试外呼任务的描述信息',
    expandable: true,
};
```

## 注意事项

1. **依赖Mock**: 测试中对Antd组件和React Native组件进行了Mock，确保测试环境的稳定性
2. **异步处理**: 某些交互测试使用了`fireEvent`来模拟用户操作
3. **样式测试**: Web版本测试包含了CSS样式和类名的验证
4. **边界情况**: 测试覆盖了空数据、零值、100%进度等边界情况
5. **类型安全**: 所有测试都使用了TypeScript类型定义，确保类型安全

## 预期测试结果

运行测试后，应该看到类似以下的输出：

```
PASS  src/components/PieChart/__tests__/PieChart.test.tsx
PASS  src/components/AICallRecord/__tests__/AICallRecord.test.tsx
PASS  src/components/MessageBox/Answer/AnswerContent/__tests__/textMessageUtils.integration.test.tsx

Test Suites: 3 passed, 3 total
Tests:       XX passed, XX total
Snapshots:   0 total
Time:        X.XXXs
```

如果测试失败，请检查：
1. 组件实现是否与测试期望一致
2. Mock配置是否正确
3. 测试环境依赖是否完整

## 后续维护

当组件功能发生变化时，请相应更新测试用例：
1. 新增功能时添加对应测试
2. 修改功能时更新相关测试
3. 删除功能时移除对应测试
4. 保持测试覆盖率在合理水平
