import { apiCaller, instance } from '@mfe/cc-api-caller-pc';
import { getPrefix } from './getPrefix';
import { message } from 'antd';

const searchParams = new URLSearchParams(location.search);
const queryHost = searchParams.get('yapiHost');

// 防止无限重定向的标记，使用tab级别的存储
const COOKIE_EXPIRED_KEY = 'cookie_expired_refresh_flag';

const handleCookieExpired = () => {
    // 检查当前tab是否已经处理过cookie过期
    const hasRefreshed = sessionStorage.getItem(COOKIE_EXPIRED_KEY);

    if (!hasRefreshed) {
        // 标记当前tab已处理cookie过期，防止无限重定向
        sessionStorage.setItem(COOKIE_EXPIRED_KEY, 'true');
        message.error('登录信息已过期，即将为您自动刷新页面~');
        setTimeout(() => {
            // 刷新页面重定向到登录页
            window.location.reload();
        }, 1500);
    }
};

export const register = () => {
    // 配置apiCaller全局设置
    apiCaller.globalConfig = {
        host: queryHost || undefined,
        prefix: path => (queryHost ? '' : getPrefix(path)),
    };

    // 添加响应拦截器来处理50102状态码（cookie过期）
    instance.interceptors.response.use(
        response => {
            // 检查响应数据中的状态码是否为50102，表示cookie已过期
            if (response && (response as any)?.code === 50102) {
                handleCookieExpired();
            }
            return response;
        },
        error => {
            return Promise.reject(error);
        },
    );
};
