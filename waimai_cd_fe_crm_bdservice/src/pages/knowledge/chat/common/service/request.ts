import { apiCaller } from '@mfe/cc-api-caller-pc';
import useRefreshSession from '@src/pages/knowledge/chat/common/service/refreshSession';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import { App } from 'antd';

// 主要功能，收口请求，在res.code = 1时刷新会话，并注入当前sessionId
const RequestType: ('get' | 'post' | 'send')[] = ['get', 'post', 'send'];
const useCallerRequest = () => {
    const refreshSession = useRefreshSession();
    const { message } = App.useApp();
    const getLatestSessionId = useAiStore(state => state.getLatestSessionId);
    const finalRequest = RequestType.map(v => ({
        [v]: async (path, params = {}, config: Parameters<typeof apiCaller.get>[2] = {}) => {
            // @ts-ignore
            const res = await apiCaller[v](
                path,
                { ...params, sessionId: getLatestSessionId() },
                { ...config, silent: true } as any,
            ); // 自动注入sessionId
            if (res.code === 1) {
                await refreshSession(true);
                return finalRequest[v](path, params, config);
            }
            if (![0, 1].includes(res.code) && !config.silent) {
                message.error(res.msg);
            }
            return res;
        },
    })).reduce((pre, cur) => ({ ...pre, ...cur }), {});
    return finalRequest;
};
export default useCallerRequest;
