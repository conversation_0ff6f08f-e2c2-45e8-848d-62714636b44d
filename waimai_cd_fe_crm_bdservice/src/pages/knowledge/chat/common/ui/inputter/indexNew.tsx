import { Input, InputRef, Image, Divider, Tooltip, Space, Upload } from 'antd';
import useSendMessage from '../../service/sendMessage/sendMessage';
import { useRef, useState, CSSProperties, useEffect } from 'react';
import { useKeyPress } from 'ahooks';
import Toolbar from './toolbar';
import useAiStore from '../../data/core';
import useOnOpenPoiSelector from '@src/pages/knowledge/chat/common/utils/openPoiSelector';
import Condition from '@src/components/Condition/Condition';
import { useSourceConfig } from '../../service/sourceParams';
import { FeedbackType, MessageFrom, MessageStatus } from '../../type/message';
import SendImg from '@src/assets/images/chat/submit.png';
import SendDisabledImg from '@src/assets/images/chat/submit_disabled.png';
import StopImg from '@src/assets/images/chat/stop_generate.png';
import FileList from './fileList';
import PoiImg from '@src/assets/images/chat/poi.png';
import AlbumImg from '@src/assets/images/chat/album.png';
import _ from 'lodash';
import { useImageUpload } from '../../hooks/useImageUpload';
import useClientWidth from '../../utils/screenWidth';
import './style.scss';
import useCallerRequest from '../../service/request';

interface Props {
    style?: CSSProperties;
}
const AiInput = (props: Props) => {
    const { style = {} } = props;
    const inputRef = useRef<InputRef>(null);
    const toolbarIconRef = useRef<HTMLDivElement>(null);
    const sourceConfig = useSourceConfig();
    const sendMessage = useSendMessage();

    const inputValue = useAiStore(v => v.inputText);
    const setInputValue = useAiStore(v => v.setInputText);
    const appendMessage = useAiStore(v => v.appendMessage);
    const { handleUpload } = useImageUpload();
    const showHome = useAiStore(v => v.config.ui.showHome);
    const file = useAiStore(v => v.file);
    const canSendMessage = inputValue?.length || file.filter(item => item.status === 'success').length > 0;
    const isGenerating = useAiStore(v => v.isGenerating());
    const stopPollingMessage = useAiStore(v => v.stopPollingMessage);

    const onSendMessage = () => {
        if (!canSendMessage) return;
        showHome &&
            appendMessage({
                localStatus: MessageStatus.done,
                from: MessageFrom.middle,
                id: _.uniqueId('message_'),
                data: [
                    {
                        insert: '点击查看历史会话',
                        type: 'text',
                        localId: _.uniqueId('openSession_'),
                    },
                ],
            });
        sendMessage(inputValue, {}, true, () => setInputValue(''));
    };

    const onOpenPoiSelector = useOnOpenPoiSelector();
    const setOnOpenPoiSelector = useAiStore(v => v.setOnOpenPoiSelector);
    useEffect(() => {
        setOnOpenPoiSelector(onOpenPoiSelector);
    }, []);
    // 支持回车发送消息&shift+回车换行，因为按键监听是全局的，所以要配合input是否focus食用
    const [isFocus, setIsFocus] = useState(false);
    const onFocus = () => {
        setIsFocus(true);
    };
    const onBlur = () => {
        setIsFocus(false);
    };
    useKeyPress(
        'enter',
        e => {
            if (!isFocus) return;
            e.preventDefault();
            if (!inputValue.trim().length) {
                return;
            }
            onSendMessage();
        },
        { exactMatch: true },
    );
    const toolbarConfig = useAiStore(v => v.toolbar);
    const { getWidth } = useClientWidth();

    const callerRequest = useCallerRequest();
    const submitFeedback = async (type, chatRecordId, feedBackContent?) => {
        await callerRequest.send(
            '/bee/v1/bdaiassistant/feedBackForChat',
            {
                chatRecordId,
                type,
                feedBackContent,
            },
            { silent: true },
        );
    };

    const getStoreEle = useAiStore(v => v.getStoreEle);

    return (
        <div style={{ width: getWidth() - 40, margin: '0 auto' }} className={file.length > 0 ? 'with-file' : ''}>
            <FileList />
            <Condition condition={[toolbarConfig.show]}>
                <Toolbar />
            </Condition>
            <div
                style={{
                    borderTop: file.length === 0 ? '1px solid #EEE' : 'none',
                    borderRadius: '0 0 12px 12px',
                    background: '#fff',
                    display: 'flex',
                    maxHeight: 156,
                    position: 'relative',
                    minHeight: 94,
                    alignItems: 'center',
                    paddingRight: 38,
                    margin: '0 auto',
                    ...style,
                }}
                ref={toolbarIconRef}
            >
                <Input.TextArea
                    onFocus={onFocus}
                    onBlur={onBlur}
                    placeholder={'有什么问题尽管问我'}
                    autoSize={{ minRows: 3, maxRows: 3 }}
                    style={{
                        ...style,
                        border: 'none',
                        flex: 1,
                        borderRadius: 24,
                        fontSize: 14,
                        padding: '0 11px',
                        paddingRight: 100,
                    }}
                    ref={inputRef}
                    value={inputValue}
                    onChange={e => {
                        setInputValue(e.target.value);
                    }}
                    onPaste={e => {
                        const items = e.clipboardData?.items;
                        if (!items) return;
                        for (let i = 0; i < items.length; i++) {
                            const item = items[i];
                            if (item.kind === 'file' && item.type.startsWith('image/')) {
                                const file = item.getAsFile();
                                if (file) {
                                    // 调用已有上传逻辑
                                    handleUpload(file);
                                    e.preventDefault(); // 阻止默认粘贴行为
                                }
                            }
                        }
                    }}
                />
                <div style={{ position: 'absolute', bottom: 8, right: 6, outline: 1 }}>
                    <Condition condition={[sourceConfig.needPoiSelector]}>
                        <Tooltip title={'上传图片'}>
                            <Upload showUploadList={false} accept=".png,.jpg,.jpeg" beforeUpload={handleUpload}>
                                <Image src={AlbumImg} width={24} preview={false} className={'pointer'} />
                            </Upload>
                        </Tooltip>
                    </Condition>
                    <Divider type="vertical" style={{ margin: '0 12px', opacity: 0 }} />
                    <Condition condition={[sourceConfig.needPoiSelector]}>
                        <Tooltip title={'商家选择'}>
                            <Image
                                src={PoiImg}
                                width={24}
                                preview={false}
                                className={'pointer'}
                                onClick={onOpenPoiSelector}
                            />
                        </Tooltip>
                    </Condition>
                    <Divider type="vertical" style={{ margin: '0 12px', opacity: 0 }} />
                    <Condition condition={[isGenerating, !isGenerating]}>
                        <Tooltip title={'停止回答'}>
                            <Space
                                onClick={() => {
                                    const pollingMessageLocalId = getStoreEle('pollingMessageLocalId');
                                    const messageArray = getStoreEle('messageArray');
                                    const message = messageArray.find(item => item.id === pollingMessageLocalId);
                                    if (message) {
                                        submitFeedback(FeedbackType.block_answer, message.serverId);
                                    }
                                    stopPollingMessage(true);
                                }}
                                className="pointer"
                            >
                                <Image src={StopImg} width={36} preview={false} />
                            </Space>
                        </Tooltip>
                        <Tooltip title={'发送'}>
                            <Image
                                src={inputValue.trim()?.length ? SendImg : SendDisabledImg}
                                width={36}
                                preview={false}
                                onClick={onSendMessage}
                                className={'pointer'}
                            />
                        </Tooltip>
                    </Condition>
                </div>
            </div>
        </div>
    );
};
export default AiInput;
