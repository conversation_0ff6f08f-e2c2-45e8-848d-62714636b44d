.pie-chart-card {
    margin: 12px 0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-card-head-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }
    }

    .ant-card-body {
        padding: 16px;
    }
}

.pie-chart-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24px;
    justify-content: center;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
    }
}

.pie-chart-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pie-chart-svg {
    .pie-slice {
        transition: opacity 0.2s ease;
        cursor: pointer;

        &:hover {
            opacity: 0.8;
        }
    }
}

.pie-chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;

    .total-label {
        font-size: 12px;
        color: #666666;
        margin-bottom: 4px;
    }

    .total-value {
        font-size: 24px;
        font-weight: 600;
        color: #333333;
    }
}

.pie-chart-legend {
    min-width: 200px;
    max-width: 300px;

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f5f5f5;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .legend-text {
            flex: 1;
            font-size: 14px;
            color: #333333;
        }

        .legend-value {
            font-size: 14px;
            color: #666666;
            font-weight: 500;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .pie-chart-wrapper {
        gap: 12px;
    }

    .pie-chart-center {
        .total-value {
            font-size: 20px;
        }
    }

    .pie-chart-legend {
        .legend-item {
            margin-bottom: 6px;
            padding: 3px 6px;

            .legend-text,
            .legend-value {
                font-size: 13px;
            }
        }
    }
}
