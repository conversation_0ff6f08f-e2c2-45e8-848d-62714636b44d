import React from 'react';
import { Card } from 'antd';
import './PieChart.scss';

interface PieChartData {
    label: string;
    value: number;
    color: string;
}

interface PieChartProps {
    data: PieChartData[];
    size?: number;
    showLegend?: boolean;
    title?: string;
}

const PieChart: React.FC<PieChartProps> = ({ 
    data, 
    size = 200, 
    showLegend = true, 
    title 
}) => {
    // 计算总值
    const total = data.reduce((sum, item) => sum + item.value, 0);

    // 计算每个数据项的百分比
    const dataWithPercentage = data.map(item => ({
        ...item,
        percentage: total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0'
    }));

    // 生成SVG饼图路径
    const generatePieSlices = () => {
        if (total === 0) return [];

        const radius = (size - 40) / 2;
        const centerX = size / 2;
        const centerY = size / 2;
        
        let currentAngle = -90; // 从顶部开始
        
        return dataWithPercentage.map((item, index) => {
            const angle = (item.value / total) * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            
            // 转换为弧度
            const startAngleRad = (startAngle * Math.PI) / 180;
            const endAngleRad = (endAngle * Math.PI) / 180;
            
            // 计算起点和终点
            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);
            
            // 大弧标志
            const largeArcFlag = angle > 180 ? 1 : 0;
            
            // 生成路径
            const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
            ].join(' ');

            currentAngle += angle;

            return {
                ...item,
                pathData,
                key: `slice-${index}`
            };
        });
    };

    const slices = generatePieSlices();

    // 渲染SVG饼图
    const renderPieChart = () => {
        return (
            <div className="pie-chart-container" style={{ width: size, height: size }}>
                <svg width={size} height={size} className="pie-chart-svg">
                    {slices.map((slice) => (
                        <path
                            key={slice.key}
                            d={slice.pathData}
                            fill={slice.color}
                            stroke="#ffffff"
                            strokeWidth="2"
                            className="pie-slice"
                        />
                    ))}
                </svg>
                
                {/* 中心文本 */}
                <div className="pie-chart-center">
                    <div className="total-label">总计</div>
                    <div className="total-value">{total}</div>
                </div>
            </div>
        );
    };

    // 渲染图例
    const renderLegend = () => {
        if (!showLegend) return null;

        return (
            <div className="pie-chart-legend">
                {dataWithPercentage.map((item, index) => (
                    <div key={index} className="legend-item">
                        <div 
                            className="legend-color" 
                            style={{ backgroundColor: item.color }}
                        />
                        <span className="legend-text">{item.label}</span>
                        <span className="legend-value">
                            {item.value} ({item.percentage}%)
                        </span>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <Card className="pie-chart-card" title={title}>
            <div className="pie-chart-wrapper">
                {renderPieChart()}
                {renderLegend()}
            </div>
        </Card>
    );
};

export default PieChart;
