import React, { useState } from 'react';
import { Card, Progress, Tag, Button, Collapse, Row, Col, Statistic } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import './AICallRecord.scss';

const { Panel } = Collapse;

interface AICallRecordProps {
    data: {
        taskId: string;
        taskName: string;
        status: 'pending' | 'running' | 'completed' | 'failed';
        progress: number; // 0-100
        totalCount: number;
        completedCount: number;
        successCount: number;
        failedCount: number;
        createTime: string;
        updateTime: string;
        description?: string;
        expandable?: boolean;
    };
}

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const [expanded, setExpanded] = useState(false);

    const {
        taskName,
        status,
        progress,
        totalCount,
        completedCount,
        successCount,
        failedCount,
        createTime,
        updateTime,
        description,
        expandable = true
    } = data;

    // 获取状态显示信息
    const getStatusConfig = () => {
        switch (status) {
            case 'pending':
                return { text: '待执行', color: 'warning' };
            case 'running':
                return { text: '执行中', color: 'processing' };
            case 'completed':
                return { text: '已完成', color: 'success' };
            case 'failed':
                return { text: '执行失败', color: 'error' };
            default:
                return { text: '未知', color: 'default' };
        }
    };

    const statusConfig = getStatusConfig();

    // 获取进度条颜色
    const getProgressColor = () => {
        switch (status) {
            case 'running':
                return '#1890ff';
            case 'completed':
                return '#52c41a';
            case 'failed':
                return '#ff4d4f';
            default:
                return '#faad14';
        }
    };

    // 渲染统计信息
    const renderStats = () => {
        return (
            <Row gutter={16} className="stats-row">
                <Col span={6}>
                    <Statistic 
                        title="总数" 
                        value={totalCount} 
                        valueStyle={{ fontSize: '18px', fontWeight: 600 }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic 
                        title="已完成" 
                        value={completedCount}
                        valueStyle={{ fontSize: '18px', fontWeight: 600 }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic 
                        title="成功" 
                        value={successCount}
                        valueStyle={{ fontSize: '18px', fontWeight: 600, color: '#52c41a' }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic 
                        title="失败" 
                        value={failedCount}
                        valueStyle={{ fontSize: '18px', fontWeight: 600, color: '#ff4d4f' }}
                    />
                </Col>
            </Row>
        );
    };

    // 渲染详细信息
    const renderDetails = () => {
        return (
            <div className="details-container">
                <Row gutter={[16, 8]}>
                    <Col span={12}>
                        <div className="detail-item">
                            <span className="detail-label">创建时间：</span>
                            <span className="detail-value">{createTime}</span>
                        </div>
                    </Col>
                    <Col span={12}>
                        <div className="detail-item">
                            <span className="detail-label">更新时间：</span>
                            <span className="detail-value">{updateTime}</span>
                        </div>
                    </Col>
                    {description && (
                        <Col span={24}>
                            <div className="detail-item">
                                <span className="detail-label">描述：</span>
                                <div className="detail-description">{description}</div>
                            </div>
                        </Col>
                    )}
                </Row>
            </div>
        );
    };

    return (
        <Card className={`ai-call-record-card status-${status}`}>
            <div className="card-header">
                <div className="title-section">
                    <h3 className="task-name">{taskName}</h3>
                    <Tag color={statusConfig.color} className="status-tag">
                        {statusConfig.text}
                    </Tag>
                </div>
                
                {expandable && (
                    <Button 
                        type="text" 
                        size="small"
                        icon={expanded ? <UpOutlined /> : <DownOutlined />}
                        onClick={() => setExpanded(!expanded)}
                        className="expand-button"
                    >
                        {expanded ? '收起' : '展开'}
                    </Button>
                )}
            </div>

            <div className="progress-section">
                <Progress 
                    percent={progress} 
                    strokeColor={getProgressColor()}
                    showInfo={true}
                    size="small"
                />
            </div>

            {renderStats()}

            {expanded && renderDetails()}
        </Card>
    );
};

export default AICallRecord;
