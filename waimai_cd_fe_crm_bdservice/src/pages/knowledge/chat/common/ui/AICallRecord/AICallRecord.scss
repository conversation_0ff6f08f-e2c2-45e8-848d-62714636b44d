.ai-call-record-card {
    margin: 12px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: #ffffff;
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;

    // 左侧状态条
    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background-color: var(--status-color, #d9d9d9);
    }

    // 不同状态的颜色
    &.status-pending::before {
        background-color: #faad14;
    }

    &.status-running::before {
        background-color: #1890ff;
    }

    &.status-completed::before {
        background-color: #52c41a;
    }

    &.status-failed::before {
        background-color: #ff4d4f;
    }

    .ant-card-body {
        padding: 16px 16px 16px 20px; // 左侧多留4px给状态条
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;

        .title-section {
            flex: 1;
            margin-right: 12px;

            .task-name {
                font-size: 16px;
                font-weight: 600;
                color: #333333;
                margin: 0 0 8px 0;
                line-height: 1.4;
            }

            .status-tag {
                font-size: 12px;
                font-weight: 500;
                border-radius: 12px;
                padding: 2px 8px;
                margin: 0;
            }
        }

        .expand-button {
            color: #1890ff;
            padding: 4px 8px;
            height: auto;
            
            &:hover {
                color: #40a9ff;
                background-color: #f0f8ff;
            }
        }
    }

    .progress-section {
        margin-bottom: 16px;

        .ant-progress {
            .ant-progress-text {
                font-size: 12px;
                font-weight: 500;
            }
        }
    }

    .stats-row {
        padding: 12px 0;
        border-top: 1px solid #f0f0f0;

        .ant-statistic {
            text-align: center;

            .ant-statistic-title {
                font-size: 12px;
                color: #666666;
                margin-bottom: 4px;
            }

            .ant-statistic-content {
                .ant-statistic-content-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                }
            }
        }
    }

    .details-container {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;

        .detail-item {
            margin-bottom: 8px;

            .detail-label {
                font-size: 14px;
                color: #666666;
                font-weight: 500;
            }

            .detail-value {
                font-size: 14px;
                color: #333333;
                margin-left: 8px;
            }

            .detail-description {
                font-size: 14px;
                color: #333333;
                line-height: 1.6;
                margin-top: 4px;
                padding: 8px 12px;
                background-color: #f9f9f9;
                border-radius: 6px;
                border-left: 3px solid #1890ff;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .ai-call-record-card {
        margin: 8px 0;

        .ant-card-body {
            padding: 12px 12px 12px 16px !important;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 12px;

            .title-section {
                margin-right: 0;
                margin-bottom: 8px;
                width: 100%;

                .task-name {
                    font-size: 15px;
                }
            }

            .expand-button {
                align-self: flex-end;
            }
        }

        .stats-row {
            .ant-col {
                margin-bottom: 8px;
            }

            .ant-statistic {
                .ant-statistic-content-value {
                    font-size: 16px !important;
                }
            }
        }

        .details-container {
            .detail-item {
                .detail-label,
                .detail-value {
                    font-size: 13px;
                }
            }
        }
    }
}

@media (max-width: 576px) {
    .ai-call-record-card {
        .stats-row {
            .ant-col {
                span: 12 !important;
                margin-bottom: 12px;
            }
        }
    }
}
