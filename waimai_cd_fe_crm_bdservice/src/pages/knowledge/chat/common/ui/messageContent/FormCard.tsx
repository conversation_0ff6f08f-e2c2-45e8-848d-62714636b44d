import React, { useState, useEffect, useRef, useContext } from 'react';
import { Input, Space, Typography, Tooltip, message as AntdMessage } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import './FormCard.scss';
import useSendMessage, { EntryPoint } from '../../service/sendMessage/sendMessage';
import MessageContext from '../message/messageContext';

interface FormCardProps {
    config: {
        label: string;
        type: 'radio' | 'input';
        options?: string[]; // input不需要
        defaultValue?: string; // 默认值
        tooltip?: string; // label后的问号的提示词，蜜蜂端点击弹窗展示，pc端hover展示
        labelWrap?: boolean; // 是否换行
        regExp?: string; // 正则校验规则，任一项不通过则提示messge并拦截按钮操作
        message?: string; // 错误提示
        required?: boolean; // 是否必填, 默认必填
    }[];
    buttonText?: string;
    history?: boolean;
    title?: string;
    subTitle?: string;
    labelSpan?: number;
}

const isValidValue = value => {
    return value === 0 || !!value;
};

const FormCard: React.FC<FormCardProps> = ({ config, buttonText = '确定', history = false, title, subTitle }) => {
    const [formValues, setFormValues] = useState<Record<string, string>>(
        config.reduce((acc, item) => {
            if (item.defaultValue) {
                acc[item.label] = item.defaultValue;
            }
            return acc;
        }, {} as Record<string, string>),
    );
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [maxLabelWidth, setMaxLabelWidth] = useState(0);
    const labelRefs = useRef<(HTMLDivElement | null)[]>([]);
    const sendMessage = useSendMessage();

    const { setWithGradientHeader } = useContext(MessageContext);
    useEffect(() => {
        setWithGradientHeader(true);
    }, []);

    useEffect(() => {
        // 计算所有label中最大的宽度
        const calculateMaxWidth = () => {
            const widths = labelRefs.current.filter(Boolean).map(ref => ref?.getBoundingClientRect().width || 0);
            const maxWidth = Math.max(...widths);
            if (maxWidth > 0 && maxWidth !== maxLabelWidth) {
                setMaxLabelWidth(maxWidth);
            }
        };

        calculateMaxWidth();
        // 监听窗口大小变化，重新计算宽度
        window.addEventListener('resize', calculateMaxWidth);
        return () => window.removeEventListener('resize', calculateMaxWidth);
    }, [config]);

    const handleChange = (label: string, value: string) => {
        const newValues = {
            ...formValues,
            [label]: value,
        };
        setFormValues(newValues);
        // 立即验证新值并更新错误状态
        const newErrors: Record<string, string> = {};
        config.forEach(item => {
            const itemValue = newValues[item.label];
            // 只有后端显示声明为 false，才为非必填项
            if (!(item.required === false) && !isValidValue(itemValue)) {
                newErrors[item.label] = `${item.label}是必填项`;
                return;
            }
            // 如果有正则校验规则
            if (item.regExp && itemValue) {
                try {
                    const regex = new RegExp(item.regExp);
                    if (!regex.test(itemValue)) {
                        newErrors[item.label] = item.message || `${item.label}格式不正确`;
                    }
                } catch (error) {
                    console.warn('Invalid regex pattern:', item.regExp);
                }
            }
        });
        setErrors(newErrors);
    };

    // 表单验证函数
    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};
        let isValid = true;
        config.forEach(item => {
            const value = formValues[item.label];
            // 只有后端显示声明为 false，才为非必填项
            if (!(item.required === false) && !isValidValue(value)) {
                newErrors[item.label] = `${item.label}是必填项`;
                isValid = false;
                return;
            }

            // 如果有正则校验规则
            if (item.regExp && value) {
                try {
                    const regex = new RegExp(item.regExp);
                    if (!regex.test(value)) {
                        newErrors[item.label] = item.message || `${item.label}格式不正确`;
                        isValid = false;
                    }
                } catch (error) {
                    console.warn('Invalid regex pattern:', item.regExp);
                }
            }
        });
        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = () => {
        // 先进行表单验证
        if (!validateForm()) {
            // 显示第一个错误信息
            const firstError = Object.values(errors)[0];
            if (firstError) {
                AntdMessage.error(firstError);
            }
            return;
        }
        setIsSubmitted(true);
        const message = Object.entries(formValues)
            .map(([label, value]) => `${label}: ${value}`)
            .join('\n');
        sendMessage(message, { entryPoint: EntryPoint.form_input });
    };

    const isDisabled = history || isSubmitted;

    return (
        <div className="form-card">
            {title && (
                <div className="form-title">
                    <Typography.Title level={5} className="form-title-text">
                        {title}
                    </Typography.Title>
                    <img
                        src="https://s3plus.meituan.net/bdaiassistant-public/rn_assets/common/yellow_ball.png"
                        className="title-decoration"
                        alt="decoration"
                    />
                </div>
            )}
            {subTitle && (
                <Typography.Text type="secondary" className="form-subtitle">
                    {subTitle}
                </Typography.Text>
            )}
            <Space direction="vertical" size={16} style={{ width: '100%' }}>
                {config.map((item, index) => {
                    return (
                        <div key={index} className={`form-item ${item.labelWrap ? 'label-wrap' : ''}`}>
                            <div
                                ref={el => (labelRefs.current[index] = el)}
                                className="form-label-container"
                                style={{
                                    width: maxLabelWidth ? maxLabelWidth : undefined,
                                    marginRight: '16px',
                                }}
                            >
                                <Typography.Text className="form-label">{item.label}</Typography.Text>
                                {item.tooltip && (
                                    <Tooltip title={item.tooltip}>
                                        <QuestionCircleOutlined className="tooltip-icon" />
                                    </Tooltip>
                                )}
                            </div>
                            <div className="form-content" style={{ flex: 1 }}>
                                {item.type === 'input' ? (
                                    <>
                                        <Input
                                            value={formValues[item.label] || ''}
                                            onChange={e => handleChange(item.label, e.target.value)}
                                            placeholder={`请输入${item.label}`}
                                            disabled={isDisabled}
                                        />
                                        {errors[item.label] ? (
                                            <Typography.Text type="danger">{errors[item.label]}</Typography.Text>
                                        ) : null}
                                    </>
                                ) : (
                                    <div className="custom-radio-group">
                                        {item.options?.map(option => (
                                            <button
                                                key={option}
                                                type="button"
                                                className={`radio-button ${
                                                    formValues[item.label] === option ? 'active' : ''
                                                }`}
                                                onClick={() => handleChange(item.label, option)}
                                                disabled={isDisabled}
                                            >
                                                {option}
                                            </button>
                                        ))}
                                        {errors[item.label] ? (
                                            <Typography.Text type="danger">{errors[item.label]}</Typography.Text>
                                        ) : null}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
                {!history && !isSubmitted && (
                    <button onClick={handleSubmit} className="submit-button">
                        {buttonText}
                    </button>
                )}
            </Space>
        </div>
    );
};

export default FormCard;
