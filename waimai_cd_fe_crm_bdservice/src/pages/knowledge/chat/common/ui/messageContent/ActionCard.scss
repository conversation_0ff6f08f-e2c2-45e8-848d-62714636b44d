.action-card {
    background: linear-gradient(102deg, rgb(184 193 255 / 17%) 0%, rgba(196, 229, 255, 0) 100%);
    border: 2px solid #B8C1FF72;
    box-shadow: none !important;

    .ant-card-body {
        padding: 12px !important;
    }

    .action-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }

    .action-card-title {
        color: #6047FA;
        font-size: 16px;
        font-weight: 700;
        margin: 0;
        line-height: 1.4;
        word-break: break-word;

        // 最多显示2行，超出省略
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .action-card-subtitle {
        font-size: 12px;
        color: #666666;
        margin: 0;
        line-height: 1.5;
        word-break: break-word;
        margin-top: 4px;
    }

    .action-card-button-wrapper {
        &.multiple-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ant-btn {
            border-radius: 24px!important;
            padding: 8px 20px;
            font-weight: 500;
            font-size: 12px;
            line-height: 18px;
            border: none;
            height: initial;

            &.ant-btn-primary {
                background-color: #ffdd10;
                border-color: #ffdd10;
                color: #333333;

                &:hover {
                    background-color: #ffcc00!important;
                    border-color: #ffcc00!important;
                }

                &:focus {
                    background-color: #ffcc00;
                    border-color: #ffcc00;
                }
            }

            &.ant-btn-default {
                background-color: #f5f5fa;
                border-color: #e0e0e0;
                color: #333333;

                &:hover {
                    background-color: #ebebeb!important;
                    border-color: #d0d0d0!important;
                }

                &:focus {
                    background-color: #ebebeb;
                    border-color: #d0d0d0;
                }
            }
        }

        // 多按钮时的样式调整 - 根据UI稿垂直排列
        &.multiple-buttons .ant-btn {
            width: 100%;
            min-width: 0;
            padding: 12px 16px;
            font-size: 14px;
            height: 44px;
            border-radius: 6px;
            font-weight: 500;

            &.ant-btn-primary {
                background-color: #6047FA;
                border-color: #6047FA;
                color: #ffffff;

                &:hover {
                    background-color: #4c38c8!important;
                    border-color: #4c38c8!important;
                }
            }

            &.ant-btn-default {
                background-color: #ffffff;
                border: 1px solid #d9d9d9;
                color: #333333;

                &:hover {
                    border-color: #6047FA!important;
                    color: #6047FA!important;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .action-card {
        margin: 8px 0;

        .ant-card-body {
            padding: 16px !important;
        }

        .action-card-title {
            font-size: 15px;
        }

        .action-card-subtitle {
            font-size: 11px;
        }

        .action-card-button-wrapper {
            &.multiple-buttons .ant-btn {
                height: 40px;
                font-size: 13px;
                padding: 10px 14px;
            }
        }
    }
}