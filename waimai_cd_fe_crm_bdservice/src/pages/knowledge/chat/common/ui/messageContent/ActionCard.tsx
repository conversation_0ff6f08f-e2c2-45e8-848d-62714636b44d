import React from 'react';
import { Card, Button, message } from 'antd';
import { ActionCardMessage, ActionCardButton } from '@src/pages/knowledge/chat/common/type/message';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { EntryPointType, EntryPoint } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import { useAICallModal } from '../../hooks/useAICallModal';
import AICallModal from '../AICallModal/AICallModal';
import './ActionCard.scss';

interface Props {
    data: ActionCardMessage['insert']['actionCard'];
    serverId?: string;
    history?: boolean;
}

const ActionCard: React.FC<Props> = ({ data, serverId, history }) => {
    const { button, buttonList, title, subTitle, backgroundColor } = data;
    const sendMessage = useSendMessage();
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();
    const { visible, openModal, closeModal, submitTask, initialParams } = useAICallModal();

    const handleButtonClick = async (buttonData: ActionCardButton) => {
        try {
            // 优先处理action
            if (buttonData.action === 'submitQuestion' && buttonData.question) {
                await sendMessage(buttonData.question, {
                    entryPointType: EntryPointType.USER,
                    entryPoint: EntryPoint.action_card,
                });
                return;
            }

            // 处理openAICallModal action
            if (buttonData.action === 'openAICallModal') {
                openModal(buttonData.AICallParams || {});
                return;
            }

            // 处理URL跳转
            if (buttonData.url) {
                openLink(buttonData.url, serverId, sessionId, bizInfo?.uid, history);
                return;
            }

            // 如果没有action和url，显示提示
            message.warning('暂无可执行的操作');
        } catch (error) {
            console.error('ActionCard button click error:', error);
            message.error('操作失败，请重试');
        }
    };

    const getButtonType = (buttonData: ActionCardButton) => {
        return buttonData.type === 'primary' ? 'primary' : 'default';
    };

    const getButtonStyle = (buttonData: ActionCardButton) => {
        const style: React.CSSProperties = {};

        // 优先使用自定义颜色
        if (buttonData.color) {
            style.borderColor = buttonData.color;
            if (buttonData.type === 'primary') {
                style.backgroundColor = buttonData.color;
            } else {
                style.color = buttonData.color;
            }
        }

        return style;
    };

    // 获取要显示的按钮列表，优先使用buttonList
    const getButtons = (): ActionCardButton[] => {
        if (buttonList && buttonList.length > 0) {
            // 最多显示3个按钮
            return buttonList.slice(0, 3);
        }
        if (button) {
            return [button];
        }
        return [];
    };

    const buttons = getButtons();
    const isMultipleButtons = buttons.length > 1;

    const cardStyle: React.CSSProperties = {
        backgroundColor: backgroundColor || '#f5f6fa',
        borderRadius: '12px',
        marginBottom: '16px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        margin: '12px 0',
    };

    return (
        <Card className="action-card" style={cardStyle} bordered={false}>
            <div className="action-card-content">
                <div className="action-card-title-wrapper">
                    <h3 className="action-card-title">{title}</h3>
                    <p className="action-card-subtitle">{subTitle}</p>
                </div>
                <div className={`action-card-button-wrapper ${isMultipleButtons ? 'multiple-buttons' : ''}`}>
                    {buttons.map((btn, index) => (
                        <Button
                            key={index}
                            type={getButtonType(btn)}
                            style={getButtonStyle(btn)}
                            className="action-card-button"
                            onClick={() => handleButtonClick(btn)}
                            size={isMultipleButtons ? 'middle' : 'large'}
                            block={!isMultipleButtons}
                        >
                            {btn.text}
                        </Button>
                    ))}
                </div>
            </div>

            <AICallModal
                visible={visible}
                onClose={closeModal}
                onSubmit={submitTask}
                initialParams={initialParams}
            />
        </Card>
    );
};

export default ActionCard;
