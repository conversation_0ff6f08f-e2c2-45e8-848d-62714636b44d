import React, { useState } from 'react';
import { Modal, Form, Input, InputNumber, Button, message } from 'antd';

interface AICallModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (params: any) => void;
    initialParams?: any;
}

const AICallModal: React.FC<AICallModalProps> = ({
    visible,
    onClose,
    onSubmit,
    initialParams = {}
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);
            
            const params = {
                ...values,
                ...initialParams
            };

            await onSubmit(params);
            message.success('外呼任务创建成功');
            form.resetFields();
            onClose();
        } catch (error) {
            console.error('创建外呼任务失败:', error);
            message.error('创建外呼任务失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onClose();
    };

    return (
        <Modal
            title="创建外呼任务"
            open={visible}
            onCancel={handleCancel}
            width={600}
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>,
                <Button 
                    key="submit" 
                    type="primary" 
                    loading={loading}
                    onClick={handleSubmit}
                >
                    创建任务
                </Button>
            ]}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    taskName: initialParams.taskName || '',
                    callScript: initialParams.callScript || '',
                    targetCount: initialParams.targetCount || 100,
                }}
            >
                <Form.Item
                    label="任务名称"
                    name="taskName"
                    rules={[
                        { required: true, message: '请输入任务名称' },
                        { max: 50, message: '任务名称不能超过50个字符' }
                    ]}
                >
                    <Input 
                        placeholder="请输入任务名称"
                        maxLength={50}
                        showCount
                    />
                </Form.Item>

                <Form.Item
                    label="外呼话术"
                    name="callScript"
                    rules={[
                        { required: true, message: '请输入外呼话术' },
                        { max: 500, message: '外呼话术不能超过500个字符' }
                    ]}
                >
                    <Input.TextArea
                        placeholder="请输入外呼话术内容"
                        rows={4}
                        maxLength={500}
                        showCount
                    />
                </Form.Item>

                <Form.Item
                    label="目标数量"
                    name="targetCount"
                    rules={[
                        { required: true, message: '请输入目标数量' },
                        { type: 'number', min: 1, max: 10000, message: '目标数量应在1-10000之间' }
                    ]}
                >
                    <InputNumber
                        placeholder="请输入目标数量"
                        min={1}
                        max={10000}
                        style={{ width: '100%' }}
                    />
                </Form.Item>

                <Form.Item
                    label="备注"
                    name="remark"
                >
                    <Input.TextArea
                        placeholder="请输入备注信息（可选）"
                        rows={2}
                        maxLength={200}
                        showCount
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AICallModal;
