
## 一、外呼相关

### 1. 串联次新运营绩效诊断
- actionCard 新增支持buttonList，button和buttonList字段只会出现其一
- 当存在buttonList时，button字段将被忽略
- buttonList最多支持3个按钮，超出部分将被截断
- 组件字段定义调整如下：
- 示意图如图：./actionCard.png

```typescript
interface ActionCard {
    type: 'actionCard';
    insert: {
        actionCard: {
            button?: Button;           // 单个按钮，与buttonList互斥
            buttonList?: Button[];     // 按钮列表，与button互斥，最多3个
            title: string;             // 卡片标题，最多显示1行，超出省略
            subTitle: string;          // 卡片副标题，最多显示2行，超出省略
            backgroundColor?: string;  // 卡片背景色，支持HexColor格式
        }
    }
}
```

**UI规范：**
- 单个按钮：居右显示，宽度自适应
- 多个按钮：水平排列，等宽分布，间距8px
- 按钮高度：32px，圆角16px
- 卡片内边距：16px

### 2. 新建外呼任务
- Button新增action类型openAICallModal用于打开创建外呼任务的弹窗
- AICallParams用于传递给弹窗组件，弹窗组件将由业务方提供，可以先用空组件替代
- 给弹窗组件传回调参数，发送query（AICallParams.question），并将回调结果放在hideSpan中带给后端
- 组件定义调整如下：

```typescript
interface Button {
    text: string;                                    // 必填，展示文本
    url?: string;                                    // 跳链
    question?: string;                               // 提问内容
    action?: 'submitQuestion' | 'openAICallModal';   // action > url, 可以用来定义操作
    AICallParams?: AICallParams;                     // 和openAICallModal配套使用
    color?: string;                                  // color > type，css支持的color，hexColor等
    type?: 'primary' | 'normal';                     // primary: 美团黄，normal：白色
}

interface AICallParams {
    question: string;        // 外呼任务的问题内容
    agentId?: string;        // 指定的外呼agent ID
    priority?: number;       // 任务优先级，1-10，默认5
    callbackUrl?: string;    // 任务完成后的回调地址
    metadata?: Record<string, any>; // 额外的元数据
}

interface Buttons {
    type: 'buttons';
    insert: {
        buttons: Button[];
    }
}
```

**交互流程：**
1. 用户点击openAICallModal类型按钮
2. 打开外呼任务创建弹窗，传入AICallParams参数
3. 用户在弹窗中确认或修改参数
4. 弹窗回调返回最终参数
5. 发送AICallParams.question作为消息
6. 将回调结果包装在hideSpan中传给后端

**弹窗组件规范：**
- 弹窗标题：创建外呼任务
- 支持编辑问题内容、选择agent、设置优先级
- 确认按钮：创建任务
- 取消按钮：关闭弹窗

### 3. 外呼结果推送、查询-小蜜
- 沟通任务完成后，推送任务完成消息（PC端不需要）。
- 新增“外呼任务”分类。
- 任务队列支持筛选类型（全部、商家诊断、外呼任务），默认全部。
- - 示意图如图：./taskList.png
**任务列表接口定义：**

```typescript
interface TaskListResponse {
    code: number;           // 响应码，0表示成功
    msg: string;           // 错误信息
    data: {
        jobList: TaskJob[];
    };
}

interface TaskJob {
    type: 'PoiDiagnosis' | 'AiCall';    // 任务类型
    status: 'pending' | 'running' | 'completed' | 'failed';  // 任务状态
    jobId: string;                      // 任务ID，jobId+type确定唯一任务
    jobName: string;                    // 任务名称
    createTime: number;                 // 创建时间戳
    completeTime?: number;              // 完成时间戳
    poiNum: number;                     // 商家个数
    agentName?: string;                 // agent名称，type=AiCall时有值
    operationType?: '1' | '2';          // 操作类型：1-url跳转，2-继续提问
    content?: string;                   // 操作内容：url或提问query
    itemList?: TaskItem[];              // 子任务列表，仅商家诊断任务有值
}

interface TaskItem {
    poiId: number;          // 商家ID
    poiName: string;        // 商家名称
    poiAvatar: string;      // 商家头像
    status: string;         // 子任务状态
    abilityType: string;    // 能力类型
    operationType: string;  // 操作类型
    content: string;        // 内容
}
```

**饼图组件：**
- 支持展示外呼结果统计数据
- 示意图如图：./pieChart.png
- 饼图数据格式如下：

```typescript
interface PieChart {
    type: 'pieChart';
    insert: {
        pieChart: {
            title?: string;     // 图表标题
            data: {
                label: string;  // 标签名称
                value: number;  // 数值
                color?: string; // 自定义颜色
            }[];
            showLegend?: boolean;   // 是否显示图例，默认true
            showPercent?: boolean;  // 是否显示百分比，默认true
        };
    };
}
```

**UI规范：**

- 饼图直径：200px
- 图例位置：右侧垂直排列
- 颜色方案：使用美团品牌色系
- 支持点击图例切换显示/隐藏对应扇形


### 4. CUI查询外呼任务执行进度
- 支持展示任务卡片，用于展示外呼任务的执行进度和结果
- 支持展开/收起功能，控制显示的任务数量
- 任务卡片数据格式如下：

```typescript
interface AICallRecord {
    type: 'AICallRecord';
    insert: {
        AICallRecord: {
            content: AICallRecordItem[];    // 任务记录列表
            extendButtonName: string;       // 展开按钮文本，为空则不展示按钮
            showNum: number;                // 直接展示的数量，为null则不限制
        }
    }
}

interface AICallRecordItem {
    jobName: string;                        // 任务名称
    status: 'init' | 'running' | 'success' | 'fail';  // 任务状态
    descriptions: {                         // 任务详情描述
        label: string;                      // 标签
        value: string;                      // 值
    }[];
    button: Button;                         // 操作按钮
    createTime?: number;                    // 创建时间
    completeTime?: number;                  // 完成时间
    progress?: number;                      // 进度百分比 0-100
}
```

**任务状态说明：**

- `init`: 初始化状态，任务已创建但未开始执行
- `running`: 执行中状态，任务正在进行
- `success`: 成功状态，任务执行完成且成功
- `fail`: 失败状态，任务执行失败

**UI规范：**

- 卡片间距：8px
- 卡片圆角：8px
- 卡片内边距：16px
- 状态指示器：使用不同颜色圆点表示状态
  - init: 灰色 #999999
  - running: 蓝色 #1890ff
  - success: 绿色 #52c41a
  - fail: 红色 #ff4d4f
- 展开按钮：居中显示，点击后切换显示全部/部分内容
- 进度条：仅在running状态下显示，高度4px