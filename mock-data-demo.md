# 外呼功能Mock数据演示指南

本文档提供了快速切换和测试新增外呼功能组件的完整指南。

## 🚀 快速开始

### React Web项目

1. **启动项目并启用Mock模式**
```bash
cd waimai_cd_fe_crm_bdservice
npm run dev

# 在浏览器中访问（启用Mock模式）
http://localhost:3000/knowledge/chat?devMock=1
```

2. **切换不同的Mock数据**

编辑 `src/pages/knowledge/chat/common/mock/mockMode.ts` 文件的第102行：

```typescript
// 测试单按钮ActionCard
renderMockData(mockData.actionCardSingleButton);

// 测试多按钮ActionCard
renderMockData(mockData.actionCardMultipleButtons);

// 测试饼图组件
renderMockData(mockData.pieChartData);

// 测试执行中的外呼任务
renderMockData(mockData.aiCallRecordRunning);

// 测试已完成的外呼任务
renderMockData(mockData.aiCallRecordCompleted);

// 测试失败的外呼任务
renderMockData(mockData.aiCallRecordFailed);

// 测试完整外呼流程（推荐）
renderMockData(mockData.aiCallCompleteFlow);
```

### React Native项目

1. **启动项目**
```bash
cd waimai_cd_fe_bee_assistant
npm start
```

2. **启用Mock模式**

在应用中通过MockModeIndicator组件启用，或者修改 `src/hooks/mock/useMockMode.ts` 文件的第29行：

```typescript
// 测试单按钮ActionCard
const mockData = mockServiceData.actionCardSingle;

// 测试多按钮ActionCard
const mockData = mockServiceData.actionCardMultiple;

// 测试饼图组件
const mockData = mockServiceData.pieChartData;

// 测试执行中的外呼任务
const mockData = mockServiceData.aiCallRecordRunning;

// 测试已完成的外呼任务
const mockData = mockServiceData.aiCallRecordCompleted;

// 测试完整外呼流程（推荐）
const mockData = mockServiceData.aiCallCompleteFlow;
```

## 📋 测试场景说明

### 1. ActionCard 单按钮测试
**数据**: `actionCardSingleButton` / `actionCardSingle`
**测试内容**:
- ✅ 单按钮显示
- ✅ 按钮点击提问功能
- ✅ 卡片样式和布局
- ✅ 背景色自定义

### 2. ActionCard 多按钮测试
**数据**: `actionCardMultipleButtons` / `actionCardMultiple`
**测试内容**:
- ✅ 多按钮水平布局
- ✅ 不同按钮类型（primary/normal）
- ✅ submitQuestion action
- ✅ openAICallModal action（会打开外呼任务弹窗）
- ✅ URL跳转功能

### 3. PieChart 饼图测试
**数据**: `pieChartData`
**测试内容**:
- ✅ 饼图数据渲染
- ✅ 图例显示
- ✅ 百分比计算
- ✅ 自定义颜色
- ✅ 响应式布局

### 4. AICallRecord 任务卡片测试

#### 执行中状态
**数据**: `aiCallRecordRunning`
**测试内容**:
- ✅ 进度条显示（65%）
- ✅ 状态指示器（蓝色）
- ✅ 统计数据展示
- ✅ 展开/收起功能

#### 已完成状态
**数据**: `aiCallRecordCompleted`
**测试内容**:
- ✅ 100%进度显示
- ✅ 完成状态指示器（绿色）
- ✅ 最终统计结果

#### 失败状态
**数据**: `aiCallRecordFailed`
**测试内容**:
- ✅ 失败状态指示器（红色）
- ✅ 部分进度显示
- ✅ 错误信息展示

### 5. 完整流程测试（推荐）
**数据**: `aiCallCompleteFlow`
**测试内容**:
- ✅ 多组件协同工作
- ✅ ActionCard + AICallRecord + PieChart
- ✅ 完整的外呼任务管理流程
- ✅ 真实业务场景模拟

## 🎯 重点测试功能

### ActionCard新功能
1. **buttonList支持**: 测试多按钮布局和交互
2. **openAICallModal action**: 测试外呼任务弹窗打开
3. **AICallParams传参**: 测试参数正确传递给弹窗

### PieChart组件
1. **数据可视化**: 测试不同数据量的饼图渲染
2. **交互功能**: 测试图例点击切换（Web版）
3. **响应式设计**: 测试不同屏幕尺寸下的显示效果

### AICallRecord组件
1. **状态管理**: 测试不同任务状态的正确显示
2. **进度跟踪**: 测试进度条和百分比显示
3. **展开交互**: 测试详情展开/收起功能
4. **数据统计**: 测试统计数据的正确计算和显示

## 🔧 调试技巧

### 1. 控制台调试
```javascript
// 在浏览器控制台中查看解析后的消息数据
console.log('Mock数据:', mockData);
console.log('解析后的内容:', JSON.parse(mockData.data.currentContent));
```

### 2. 组件状态检查
```typescript
// 在组件中添加调试日志
console.log('ActionCard props:', props);
console.log('PieChart data:', data);
console.log('AICallRecord status:', status);
```

### 3. 网络请求模拟
```typescript
// 检查Mock模式是否正确拦截了API请求
console.log('Mock模式状态:', isMockMode);
```

## 📊 数据结构示例

### ActionCard数据结构
```typescript
{
  type: 'actionCard',
  insert: {
    actionCard: {
      title: '卡片标题',
      subTitle: '卡片副标题',
      backgroundColor: '#f0f8ff',
      buttonList: [
        {
          text: '按钮文本',
          action: 'openAICallModal',
          AICallParams: {
            taskName: '任务名称',
            callScript: '外呼话术',
            targetCount: 100
          },
          type: 'primary'
        }
      ]
    }
  }
}
```

### PieChart数据结构
```typescript
{
  type: 'pieChart',
  insert: {
    pieChart: {
      title: '图表标题',
      data: [
        {
          label: '数据标签',
          value: 450,
          color: '#52c41a'
        }
      ],
      size: 200,
      showLegend: true
    }
  }
}
```

### AICallRecord数据结构
```typescript
{
  type: 'aiCallRecord',
  insert: {
    aiCallRecord: {
      taskId: 'task-123',
      taskName: '任务名称',
      status: 'running',
      progress: 65,
      totalCount: 1000,
      completedCount: 650,
      successCount: 420,
      failedCount: 230,
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-01-15 14:20:00',
      description: '任务描述',
      expandable: true
    }
  }
}
```

## 🚨 常见问题解决

### 1. 组件不显示
- 检查组件是否已在textMessageUtils.tsx中注册
- 确认import路径正确
- 查看控制台错误信息

### 2. 样式异常
- 检查CSS文件是否正确导入
- 确认样式类名没有冲突
- 验证响应式样式是否生效

### 3. 交互功能失效
- 检查事件处理函数是否正确绑定
- 确认Mock数据中的action类型正确
- 验证参数传递是否完整

### 4. 数据格式错误
- 确认Mock数据结构与组件期望一致
- 检查JSON格式是否有效
- 验证必填字段是否完整

## 📝 测试清单

使用以下清单确保所有功能都经过测试：

- [ ] ActionCard单按钮显示和交互
- [ ] ActionCard多按钮布局和功能
- [ ] openAICallModal弹窗打开和参数传递
- [ ] PieChart数据渲染和图例显示
- [ ] AICallRecord不同状态显示
- [ ] AICallRecord展开/收起功能
- [ ] 进度条和统计数据显示
- [ ] 响应式布局适配
- [ ] 错误状态处理
- [ ] 多组件协同工作

完成所有测试后，新的外呼功能组件就可以投入使用了！
