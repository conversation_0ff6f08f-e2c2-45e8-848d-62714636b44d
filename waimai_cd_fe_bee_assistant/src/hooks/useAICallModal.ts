import { useState, useCallback } from 'react';
import { Toast } from '@roo/roo-rn';

interface AICallParams {
    taskName?: string;
    callScript?: string;
    targetCount?: number;
    [key: string]: any;
}

interface UseAICallModalReturn {
    visible: boolean;
    openModal: (params?: AICallParams) => void;
    closeModal: () => void;
    submitTask: (params: AICallParams) => Promise<void>;
    initialParams: AICallParams;
}

export const useAICallModal = (): UseAICallModalReturn => {
    const [visible, setVisible] = useState(false);
    const [initialParams, setInitialParams] = useState<AICallParams>({});

    const openModal = useCallback((params: AICallParams = {}) => {
        setInitialParams(params);
        setVisible(true);
    }, []);

    const closeModal = useCallback(() => {
        setVisible(false);
        setInitialParams({});
    }, []);

    const submitTask = useCallback(async (params: AICallParams) => {
        try {
            // TODO: 调用实际的外呼任务创建API
            console.log('创建外呼任务:', params);
            
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            Toast.open('外呼任务创建成功');
            
            // 这里可以添加成功后的回调，比如刷新任务列表等
            
        } catch (error) {
            console.error('创建外呼任务失败:', error);
            Toast.open('创建外呼任务失败，请重试');
            throw error;
        }
    }, []);

    return {
        visible,
        openModal,
        closeModal,
        submitTask,
        initialParams,
    };
};
