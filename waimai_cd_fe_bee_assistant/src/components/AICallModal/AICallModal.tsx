import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from '@mrn/react-native';
import { Modal } from '@roo/roo-rn';
import { Toast } from '@roo/roo-rn';

interface AICallModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (params: any) => void;
    initialParams?: any;
}

const AICallModal: React.FC<AICallModalProps> = ({
    visible,
    onClose,
    onSubmit,
    initialParams = {}
}) => {
    const [taskName, setTaskName] = useState(initialParams.taskName || '');
    const [callScript, setCallScript] = useState(initialParams.callScript || '');
    const [targetCount, setTargetCount] = useState(initialParams.targetCount || '100');

    const handleSubmit = () => {
        if (!taskName.trim()) {
            Toast.open('请输入任务名称');
            return;
        }

        if (!callScript.trim()) {
            Toast.open('请输入外呼话术');
            return;
        }

        const params = {
            taskName: taskName.trim(),
            callScript: callScript.trim(),
            targetCount: parseInt(targetCount) || 100,
            ...initialParams
        };

        onSubmit(params);
        onClose();
    };

    const handleClose = () => {
        onClose();
    };

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="slide"
            onRequestClose={handleClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity 
                    style={styles.overlayBackground}
                    activeOpacity={1}
                    onPress={handleClose}
                />
                <View style={styles.modalContainer}>
                    <View style={styles.header}>
                        <Text style={styles.title}>创建外呼任务</Text>
                        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
                            <Text style={styles.closeText}>×</Text>
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                        <View style={styles.formItem}>
                            <Text style={styles.label}>任务名称 *</Text>
                            <View style={styles.inputContainer}>
                                <Text style={styles.input}>{taskName || '请输入任务名称'}</Text>
                            </View>
                        </View>

                        <View style={styles.formItem}>
                            <Text style={styles.label}>外呼话术 *</Text>
                            <View style={[styles.inputContainer, styles.textareaContainer]}>
                                <Text style={[styles.input, styles.textarea]}>
                                    {callScript || '请输入外呼话术内容'}
                                </Text>
                            </View>
                        </View>

                        <View style={styles.formItem}>
                            <Text style={styles.label}>目标数量</Text>
                            <View style={styles.inputContainer}>
                                <Text style={styles.input}>{targetCount}</Text>
                            </View>
                        </View>

                        <View style={styles.note}>
                            <Text style={styles.noteText}>
                                注：此为演示版本，实际输入功能需要集成表单组件
                            </Text>
                        </View>
                    </ScrollView>

                    <View style={styles.footer}>
                        <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
                            <Text style={styles.cancelButtonText}>取消</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
                            <Text style={styles.submitButtonText}>创建任务</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    overlayBackground: {
        flex: 1,
    },
    modalContainer: {
        backgroundColor: '#ffffff',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        maxHeight: '80%',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
    },
    closeButton: {
        width: 32,
        height: 32,
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeText: {
        fontSize: 24,
        color: '#999999',
    },
    content: {
        padding: 16,
        maxHeight: 400,
    },
    formItem: {
        marginBottom: 20,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333333',
        marginBottom: 8,
    },
    inputContainer: {
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: 8,
        padding: 12,
        backgroundColor: '#f9f9f9',
    },
    textareaContainer: {
        minHeight: 80,
    },
    input: {
        fontSize: 14,
        color: '#333333',
    },
    textarea: {
        textAlignVertical: 'top',
    },
    note: {
        backgroundColor: '#f0f8ff',
        padding: 12,
        borderRadius: 8,
        marginTop: 8,
    },
    noteText: {
        fontSize: 12,
        color: '#666666',
        lineHeight: 18,
    },
    footer: {
        flexDirection: 'row',
        padding: 16,
        gap: 12,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    cancelButton: {
        flex: 1,
        height: 44,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e0e0e0',
        backgroundColor: '#ffffff',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666666',
    },
    submitButton: {
        flex: 1,
        height: 44,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        backgroundColor: '#ffdd10',
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#333333',
    },
});

export default AICallModal;
