import { deviceEventEmitter } from '@mrn/mrn-utils';
import { useCallback, useEffect, useRef, useState } from 'react';
import { DeviceEventEmitter } from 'react-native';

import { getTaskStatus, recordPopup } from '../../../api/taskApi';
import { SOURCE } from '../../../types';

const sourceToTab = {
    [SOURCE.home]: 'Base',
    [SOURCE.wdcAdopt]: 'PoiCenter',
    [SOURCE.wdcNearby]: 'PoiCenter',
    [SOURCE.wdcAll]: 'PoiCenter',
    [SOURCE.wdcResponsible]: 'PoiCenter',
    [SOURCE.wdcConcerned]: 'PoiCenter',
    [SOURCE.dove]: 'Homer',
    [SOURCE.tabWorkbench]: 'Workbench',
    [SOURCE.tabMine]: 'Mine',
};

const useNotify = (visible: boolean, source: SOURCE) => {
    const [isShowTaskReminder, setShowTaskReminder] = useState(false);
    const bubbleTimer = useRef(null); // 气泡自动隐藏定时器
    const pollingTimer = useRef(null); // 轮询定时器
    const isPollingPaused = useRef(false); // 轮询是否暂停

    // 隐藏TaskReminderBubble并发起recordPopup请求
    const hideTaskReminder = useCallback(async () => {
        if (isShowTaskReminder) {
            try {
                await recordPopup();
            } catch (error) {
                console.error('Failed to record popup:', error);
            }
            setShowTaskReminder(false);
        }
    }, [isShowTaskReminder]);

    const fetchRunningJob = useCallback(async () => {
        try {
            const data = await getTaskStatus();
            if (data?.needToClick) {
                setShowTaskReminder(true);
            }
        } catch (error) {
            console.error('Failed to fetch running job:', error);
        }
    }, []);

    // 开始轮询
    const startPolling = useCallback(() => {
        if (!visible) {
            return;
        }
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
        }
        pollingTimer.current = setInterval(() => {
            fetchRunningJob();
        }, 10000);
        isPollingPaused.current = false;
    }, [fetchRunningJob, visible]);

    // 暂停轮询
    const pausePolling = useCallback(() => {
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
            pollingTimer.current = null;
        }
        isPollingPaused.current = true;
    }, []);

    // 轮询 fetchRunningJob
    useEffect(() => {
        fetchRunningJob(); // 初始调用
        startPolling(); // 开始轮询

        return () => {
            if (pollingTimer.current) {
                clearInterval(pollingTimer.current);
            }
        };
    }, [fetchRunningJob, startPolling]);

    // 气泡展示5s后自动隐藏
    useEffect(() => {
        if (isShowTaskReminder) {
            // 清除之前的定时器
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }

            // 设置5s后自动隐藏
            bubbleTimer.current = setTimeout(() => {
                hideTaskReminder();
            }, 5000);
        }

        return () => {
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
        };
    }, [isShowTaskReminder, hideTaskReminder]);

    // 从首页直接切换到其他bundle的场景
    useEffect(() => {
        if (!visible) {
            return;
        }
        const sub1 = deviceEventEmitter.addListener(
            'containerViewDidAppear',
            () => {
                // 如果有暂停的计时则开始计时
                if (
                    isPollingPaused.current &&
                    currentTab === sourceToTab[source]
                ) {
                    startPolling();
                }
            },
        );
        const sub2 = deviceEventEmitter.addListener(
            'containerViewDidDisappear',
            () => {
                // 如果有正在进行的计时则暂停计时
                if (pollingTimer.current) {
                    pausePolling();
                }
            },
        );
        return () => {
            sub1.remove();
            sub2.remove();
        };
    }, [startPolling, pausePolling, visible]);

    const [currentTab, setCurrentTab] = useState('Base');

    useEffect(() => {
        const sub = DeviceEventEmitter.addListener(
            'BEE_BASE_TAB_CHANGE',
            (tab) => {
                setCurrentTab(tab);
            },
        );
        return () => {
            sub.remove();
        };
    }, []);

    useEffect(() => {
        if (currentTab === sourceToTab[source]) {
            startPolling();
        } else {
            pausePolling();
        }
    }, [currentTab]);

    return {
        isShowTaskReminder,
        hideTaskReminder,
    };
};

export default useNotify;
