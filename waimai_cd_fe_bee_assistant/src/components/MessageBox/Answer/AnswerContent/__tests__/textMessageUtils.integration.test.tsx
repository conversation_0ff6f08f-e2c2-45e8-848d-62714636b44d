import React from 'react';
import { render } from '@testing-library/react-native';
import { renderMessageItem } from '../textMessageUtils';
import { PieChartMessage, AICallRecordMessage } from '../../../../../types/message';

// Mock components
jest.mock('../../../../PieChart/PieChart', () => {
    return function MockPieChart({ data, title }) {
        return (
            <div testID="pie-chart">
                <div testID="pie-chart-title">{title}</div>
                <div testID="pie-chart-data">{JSON.stringify(data)}</div>
            </div>
        );
    };
});

jest.mock('../../../../AICallRecord/AICallRecord', () => {
    return function MockAICallRecord({ data }) {
        return (
            <div testID="ai-call-record">
                <div testID="ai-call-record-name">{data.taskName}</div>
                <div testID="ai-call-record-status">{data.status}</div>
            </div>
        );
    };
});

describe('textMessageUtils Integration Tests', () => {
    describe('PieChart Message Rendering', () => {
        it('should render PieChart message correctly', () => {
            const pieChartMessage: PieChartMessage = {
                type: 'pieChart',
                insert: {
                    pieChart: {
                        data: [
                            { label: '成功', value: 150, color: '#52c41a' },
                            { label: '失败', value: 50, color: '#ff4d4f' },
                        ],
                        title: '外呼结果统计',
                        size: 200,
                        showLegend: true,
                    },
                },
            };

            const { getByTestId } = render(
                renderMessageItem(pieChartMessage, 0)
            );

            expect(getByTestId('pie-chart')).toBeTruthy();
            expect(getByTestId('pie-chart-title')).toHaveTextContent('外呼结果统计');
            
            const dataElement = getByTestId('pie-chart-data');
            const parsedData = JSON.parse(dataElement.props.children);
            expect(parsedData).toHaveLength(2);
            expect(parsedData[0]).toEqual({ label: '成功', value: 150, color: '#52c41a' });
            expect(parsedData[1]).toEqual({ label: '失败', value: 50, color: '#ff4d4f' });
        });

        it('should render PieChart message with minimal props', () => {
            const pieChartMessage: PieChartMessage = {
                type: 'pieChart',
                insert: {
                    pieChart: {
                        data: [
                            { label: '测试', value: 100, color: '#1890ff' },
                        ],
                    },
                },
            };

            const { getByTestId } = render(
                renderMessageItem(pieChartMessage, 0)
            );

            expect(getByTestId('pie-chart')).toBeTruthy();
            
            const dataElement = getByTestId('pie-chart-data');
            const parsedData = JSON.parse(dataElement.props.children);
            expect(parsedData).toHaveLength(1);
            expect(parsedData[0]).toEqual({ label: '测试', value: 100, color: '#1890ff' });
        });
    });

    describe('AICallRecord Message Rendering', () => {
        it('should render AICallRecord message correctly', () => {
            const aiCallRecordMessage: AICallRecordMessage = {
                type: 'aiCallRecord',
                insert: {
                    aiCallRecord: {
                        taskId: 'task-123',
                        taskName: '测试外呼任务',
                        status: 'running',
                        progress: 65,
                        totalCount: 1000,
                        completedCount: 650,
                        successCount: 500,
                        failedCount: 150,
                        createTime: '2024-01-15 10:30:00',
                        updateTime: '2024-01-15 14:20:00',
                        description: '测试任务描述',
                        expandable: true,
                    },
                },
            };

            const { getByTestId } = render(
                renderMessageItem(aiCallRecordMessage, 0)
            );

            expect(getByTestId('ai-call-record')).toBeTruthy();
            expect(getByTestId('ai-call-record-name')).toHaveTextContent('测试外呼任务');
            expect(getByTestId('ai-call-record-status')).toHaveTextContent('running');
        });

        it('should render AICallRecord message with different statuses', () => {
            const statuses = ['pending', 'running', 'completed', 'failed'] as const;
            
            statuses.forEach((status) => {
                const aiCallRecordMessage: AICallRecordMessage = {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            taskId: `task-${status}`,
                            taskName: `${status}任务`,
                            status,
                            progress: status === 'completed' ? 100 : 50,
                            totalCount: 100,
                            completedCount: status === 'completed' ? 100 : 50,
                            successCount: status === 'completed' ? 80 : 40,
                            failedCount: status === 'completed' ? 20 : 10,
                            createTime: '2024-01-15 10:30:00',
                            updateTime: '2024-01-15 14:20:00',
                        },
                    },
                };

                const { getByTestId } = render(
                    renderMessageItem(aiCallRecordMessage, 0)
                );

                expect(getByTestId('ai-call-record-status')).toHaveTextContent(status);
            });
        });
    });

    describe('Message Type Handling', () => {
        it('should handle unknown message types gracefully', () => {
            const unknownMessage = {
                type: 'unknownType',
                insert: {
                    unknownType: {
                        data: 'test',
                    },
                },
            };

            const { getByText } = render(
                renderMessageItem(unknownMessage as any, 0)
            );

            expect(getByText('该类型消息暂不支持，请升级后重试！')).toBeTruthy();
        });

        it('should generate correct keys for different message types', () => {
            const pieChartMessage: PieChartMessage = {
                type: 'pieChart',
                insert: {
                    pieChart: {
                        data: [{ label: '测试', value: 100, color: '#1890ff' }],
                    },
                },
            };

            const aiCallRecordMessage: AICallRecordMessage = {
                type: 'aiCallRecord',
                insert: {
                    aiCallRecord: {
                        taskId: 'task-123',
                        taskName: '测试任务',
                        status: 'running',
                        progress: 50,
                        totalCount: 100,
                        completedCount: 50,
                        successCount: 40,
                        failedCount: 10,
                        createTime: '2024-01-15 10:30:00',
                        updateTime: '2024-01-15 14:20:00',
                    },
                },
            };

            // 测试不同索引生成不同的key
            const pieChart1 = renderMessageItem(pieChartMessage, 0);
            const pieChart2 = renderMessageItem(pieChartMessage, 1);
            const aiCallRecord1 = renderMessageItem(aiCallRecordMessage, 0);
            const aiCallRecord2 = renderMessageItem(aiCallRecordMessage, 1);

            expect(pieChart1.key).not.toBe(pieChart2.key);
            expect(aiCallRecord1.key).not.toBe(aiCallRecord2.key);
            expect(pieChart1.key).not.toBe(aiCallRecord1.key);
        });
    });
});
