import React from 'react';
import { View, Text, StyleSheet } from '@mrn/react-native';

interface PieChartData {
    label: string;
    value: number;
    color: string;
}

interface PieChartProps {
    data: PieChartData[];
    size?: number;
    showLegend?: boolean;
    title?: string;
}

const PieChart: React.FC<PieChartProps> = ({ 
    data, 
    size = 200, 
    showLegend = true, 
    title 
}) => {
    // 计算总值
    const total = data.reduce((sum, item) => sum + item.value, 0);

    // 计算每个数据项的百分比
    const dataWithPercentage = data.map(item => ({
        ...item,
        percentage: total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0'
    }));

    // 环形图表示饼图
    const renderDonutChart = () => {
        const radius = (size - 40) / 2;
        const strokeWidth = 20;
        const innerRadius = radius - strokeWidth;

        return (
            <View style={[styles.chartContainer, { width: size, height: size }]}>
                {/* 背景圆环 */}
                <View
                    style={[
                        styles.circle,
                        {
                            width: size - 20,
                            height: size - 20,
                            borderRadius: (size - 20) / 2,
                            backgroundColor: '#f5f5f5',
                            borderWidth: strokeWidth,
                            borderColor: '#f0f0f0'
                        }
                    ]}
                />

                {/* 数据环形段 */}
                {dataWithPercentage.map((item, index) => {
                    const percentage = parseFloat(item.percentage);
                    const angle = (percentage / 100) * 360;
                    const startAngle = dataWithPercentage
                        .slice(0, index)
                        .reduce((sum, prev) => sum + parseFloat(prev.percentage), 0) * 3.6;

                    return (
                        <View
                            key={index}
                            style={[
                                styles.segment,
                                {
                                    position: 'absolute',
                                    width: strokeWidth,
                                    height: radius,
                                    backgroundColor: item.color,
                                    borderRadius: strokeWidth / 2,
                                    top: size / 2 - radius / 2,
                                    left: size / 2 - strokeWidth / 2,
                                    transformOrigin: `${strokeWidth / 2}px ${radius / 2}px`,
                                    transform: [
                                        { rotate: `${startAngle}deg` }
                                    ]
                                }
                            ]}
                        />
                    );
                })}

                {/* 中心文本 */}
                <View style={styles.centerContent}>
                    <Text style={styles.totalLabel}>总计</Text>
                    <Text style={styles.totalValue}>{total}</Text>
                </View>
            </View>
        );
    };

    // 渲染图例
    const renderLegend = () => {
        if (!showLegend) return null;

        return (
            <View style={styles.legendContainer}>
                {dataWithPercentage.map((item, index) => (
                    <View key={index} style={styles.legendItem}>
                        <View 
                            style={[
                                styles.legendColor, 
                                { backgroundColor: item.color }
                            ]} 
                        />
                        <Text style={styles.legendText}>
                            {item.label}
                        </Text>
                        <Text style={styles.legendValue}>
                            {item.value} ({item.percentage}%)
                        </Text>
                    </View>
                ))}
            </View>
        );
    };

    return (
        <View style={styles.container}>
            {title && <Text style={styles.title}>{title}</Text>}
            <View style={styles.contentContainer}>
                {renderDonutChart()}
                {renderLegend()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        backgroundColor: '#ffffff',
        borderRadius: 12,
        margin: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    title: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 16,
        textAlign: 'center',
    },
    chartContainer: {
        position: 'relative',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
    },
    circle: {
        position: 'absolute',
        borderWidth: 2,
        borderColor: '#e0e0e0',
    },
    centerContent: {
        position: 'absolute',
        alignItems: 'center',
        justifyContent: 'center',
    },
    totalLabel: {
        fontSize: 12,
        color: '#666666',
        marginBottom: 4,
    },
    totalValue: {
        fontSize: 24,
        fontWeight: '600',
        color: '#333333',
    },
    segment: {
        // 动态样式在组件中设置
    },
    legendContainer: {
        flex: 1,
        marginLeft: 20,
        maxWidth: 200,
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        paddingHorizontal: 8,
    },
    legendColor: {
        width: 12,
        height: 12,
        borderRadius: 6,
        marginRight: 12,
    },
    legendText: {
        fontSize: 14,
        color: '#333333',
        flex: 1,
    },
    legendValue: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '500',
    },
});

export default PieChart;
