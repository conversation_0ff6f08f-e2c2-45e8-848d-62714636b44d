import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';

interface AICallRecordProps {
    data: {
        taskId: string;
        taskName: string;
        status: 'pending' | 'running' | 'completed' | 'failed';
        progress: number; // 0-100
        totalCount: number;
        completedCount: number;
        successCount: number;
        failedCount: number;
        createTime: string;
        updateTime: string;
        description?: string;
        expandable?: boolean;
    };
}

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const [expanded, setExpanded] = useState(false);

    const {
        taskName,
        status,
        progress,
        totalCount,
        completedCount,
        successCount,
        failedCount,
        createTime,
        updateTime,
        description,
        expandable = true
    } = data;

    // 获取状态显示信息
    const getStatusInfo = () => {
        switch (status) {
            case 'pending':
                return { text: '待执行', color: '#faad14', bgColor: '#fff7e6' };
            case 'running':
                return { text: '执行中', color: '#1890ff', bgColor: '#e6f7ff' };
            case 'completed':
                return { text: '已完成', color: '#52c41a', bgColor: '#f6ffed' };
            case 'failed':
                return { text: '执行失败', color: '#ff4d4f', bgColor: '#fff2f0' };
            default:
                return { text: '未知', color: '#d9d9d9', bgColor: '#f5f5f5' };
        }
    };

    const statusInfo = getStatusInfo();

    // 渲染进度条
    const renderProgressBar = () => {
        return (
            <View style={styles.progressContainer}>
                <View style={styles.progressBackground}>
                    <View 
                        style={[
                            styles.progressFill, 
                            { 
                                width: `${progress}%`,
                                backgroundColor: statusInfo.color
                            }
                        ]} 
                    />
                </View>
                <Text style={styles.progressText}>{progress}%</Text>
            </View>
        );
    };

    // 渲染统计信息
    const renderStats = () => {
        return (
            <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>{totalCount}</Text>
                    <Text style={styles.statLabel}>总数</Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>{completedCount}</Text>
                    <Text style={styles.statLabel}>已完成</Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={[styles.statValue, { color: '#52c41a' }]}>{successCount}</Text>
                    <Text style={styles.statLabel}>成功</Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={[styles.statValue, { color: '#ff4d4f' }]}>{failedCount}</Text>
                    <Text style={styles.statLabel}>失败</Text>
                </View>
            </View>
        );
    };

    // 渲染详细信息
    const renderDetails = () => {
        if (!expanded) return null;

        return (
            <View style={styles.detailsContainer}>
                <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>创建时间：</Text>
                    <Text style={styles.detailValue}>{createTime}</Text>
                </View>
                <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>更新时间：</Text>
                    <Text style={styles.detailValue}>{updateTime}</Text>
                </View>
                {description && (
                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>描述：</Text>
                        <Text style={[styles.detailValue, styles.descriptionText]}>
                            {description}
                        </Text>
                    </View>
                )}
            </View>
        );
    };

    return (
        <View style={styles.container}>
            {/* 左侧状态条 */}
            <View style={[styles.statusBar, { backgroundColor: statusInfo.color }]} />
            <View style={styles.card}>
                {/* 头部信息 */}
                <View style={styles.header}>
                    <View style={styles.titleContainer}>
                        <Text style={styles.taskName} numberOfLines={1}>
                            {taskName}
                        </Text>
                        <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
                            <Text style={[styles.statusText, { color: statusInfo.color }]}>
                                {statusInfo.text}
                            </Text>
                        </View>
                    </View>
                    
                    {expandable && (
                        <TouchableOpacity 
                            onPress={() => setExpanded(!expanded)}
                            style={styles.expandButton}
                        >
                            <Text style={styles.expandText}>
                                {expanded ? '收起' : '展开'}
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>

                {/* 进度条 */}
                {renderProgressBar()}

                {/* 统计信息 */}
                {renderStats()}

                {/* 详细信息 */}
                {renderDetails()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        margin: 8,
        borderRadius: 8,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    statusBar: {
        width: 4,
        borderTopLeftRadius: 8,
        borderBottomLeftRadius: 8,
    },
    card: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderTopRightRadius: 8,
        borderBottomRightRadius: 8,
        padding: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    titleContainer: {
        flex: 1,
        marginRight: 12,
    },
    taskName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 8,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        alignSelf: 'flex-start',
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    expandButton: {
        paddingHorizontal: 8,
        paddingVertical: 4,
    },
    expandText: {
        fontSize: 14,
        color: '#1890ff',
    },
    progressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    progressBackground: {
        flex: 1,
        height: 6,
        backgroundColor: '#f0f0f0',
        borderRadius: 3,
        marginRight: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 3,
    },
    progressText: {
        fontSize: 12,
        color: '#666666',
        minWidth: 35,
        textAlign: 'right',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: 12,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    statItem: {
        alignItems: 'center',
    },
    statValue: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 12,
        color: '#666666',
    },
    detailsContainer: {
        marginTop: 12,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    detailRow: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    detailLabel: {
        fontSize: 14,
        color: '#666666',
        width: 80,
    },
    detailValue: {
        fontSize: 14,
        color: '#333333',
        flex: 1,
    },
    descriptionText: {
        lineHeight: 20,
    },
});

export default AICallRecord;
