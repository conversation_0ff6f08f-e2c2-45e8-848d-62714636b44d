import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AICallRecord from '../AICallRecord';

describe('AICallRecord Component', () => {
    const mockData = {
        taskId: 'task-123',
        taskName: '测试外呼任务',
        status: 'running' as const,
        progress: 65,
        totalCount: 1000,
        completedCount: 650,
        successCount: 500,
        failedCount: 150,
        createTime: '2024-01-15 10:30:00',
        updateTime: '2024-01-15 14:20:00',
        description: '这是一个测试外呼任务的描述信息',
        expandable: true,
    };

    it('should render correctly with basic props', () => {
        const { getByText } = render(<AICallRecord data={mockData} />);

        expect(getByText('测试外呼任务')).toBeTruthy();
        expect(getByText('执行中')).toBeTruthy();
        expect(getByText('65%')).toBeTruthy();
        expect(getByText('1000')).toBeTruthy(); // 总数
        expect(getByText('650')).toBeTruthy(); // 已完成
        expect(getByText('500')).toBeTruthy(); // 成功
        expect(getByText('150')).toBeTruthy(); // 失败
    });

    it('should show correct status for different states', () => {
        const pendingData = { ...mockData, status: 'pending' as const };
        const { rerender, getByText } = render(<AICallRecord data={pendingData} />);
        expect(getByText('待执行')).toBeTruthy();

        const completedData = { ...mockData, status: 'completed' as const };
        rerender(<AICallRecord data={completedData} />);
        expect(getByText('已完成')).toBeTruthy();

        const failedData = { ...mockData, status: 'failed' as const };
        rerender(<AICallRecord data={failedData} />);
        expect(getByText('执行失败')).toBeTruthy();
    });

    it('should toggle expand/collapse functionality', () => {
        const { getByText, queryByText } = render(<AICallRecord data={mockData} />);

        // 初始状态应该是收起的
        expect(queryByText('创建时间：')).toBeNull();
        expect(getByText('展开')).toBeTruthy();

        // 点击展开按钮
        fireEvent.press(getByText('展开'));
        expect(getByText('创建时间：')).toBeTruthy();
        expect(getByText('2024-01-15 10:30:00')).toBeTruthy();
        expect(getByText('更新时间：')).toBeTruthy();
        expect(getByText('2024-01-15 14:20:00')).toBeTruthy();
        expect(getByText('描述：')).toBeTruthy();
        expect(getByText('这是一个测试外呼任务的描述信息')).toBeTruthy();
        expect(getByText('收起')).toBeTruthy();

        // 点击收起按钮
        fireEvent.press(getByText('收起'));
        expect(queryByText('创建时间：')).toBeNull();
        expect(getByText('展开')).toBeTruthy();
    });

    it('should not show expand button when expandable is false', () => {
        const nonExpandableData = { ...mockData, expandable: false };
        const { queryByText } = render(<AICallRecord data={nonExpandableData} />);

        expect(queryByText('展开')).toBeNull();
        expect(queryByText('收起')).toBeNull();
    });

    it('should handle data without description', () => {
        const dataWithoutDescription = { ...mockData, description: undefined };
        const { getByText, queryByText } = render(<AICallRecord data={dataWithoutDescription} />);

        // 展开详情
        fireEvent.press(getByText('展开'));
        
        expect(getByText('创建时间：')).toBeTruthy();
        expect(getByText('更新时间：')).toBeTruthy();
        expect(queryByText('描述：')).toBeNull();
    });

    it('should display progress bar correctly', () => {
        const { getByText } = render(<AICallRecord data={mockData} />);
        
        // 检查进度百分比显示
        expect(getByText('65%')).toBeTruthy();
    });

    it('should display statistics correctly', () => {
        const { getByText } = render(<AICallRecord data={mockData} />);

        // 检查统计标签
        expect(getByText('总数')).toBeTruthy();
        expect(getByText('已完成')).toBeTruthy();
        expect(getByText('成功')).toBeTruthy();
        expect(getByText('失败')).toBeTruthy();

        // 检查统计数值
        expect(getByText('1000')).toBeTruthy();
        expect(getByText('650')).toBeTruthy();
        expect(getByText('500')).toBeTruthy();
        expect(getByText('150')).toBeTruthy();
    });

    it('should handle zero progress', () => {
        const zeroProgressData = { ...mockData, progress: 0, completedCount: 0, successCount: 0, failedCount: 0 };
        const { getByText } = render(<AICallRecord data={zeroProgressData} />);

        expect(getByText('0%')).toBeTruthy();
        expect(getByText('0')).toBeTruthy(); // 已完成数量
    });

    it('should handle 100% progress', () => {
        const fullProgressData = { 
            ...mockData, 
            progress: 100, 
            completedCount: 1000, 
            successCount: 800, 
            failedCount: 200,
            status: 'completed' as const
        };
        const { getByText } = render(<AICallRecord data={fullProgressData} />);

        expect(getByText('100%')).toBeTruthy();
        expect(getByText('已完成')).toBeTruthy();
        expect(getByText('1000')).toBeTruthy(); // 已完成数量等于总数
    });
});
